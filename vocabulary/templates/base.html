{% load i18n %} {% load static %}
<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE }}">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content="{{ csrf_token }}" />
    <title>
      {% block title %}{{ manual_texts.learn_english }}{% endblock %}
    </title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <style>
      :root {
        --primary-color: #6a6cff;
        --primary-color-dark: #5859d8;

        /* Light Mode */
        --background-main: #f3f4f6;
        --background-secondary: #ffffff;
        --background-light: #ffffff;
        --text-main: #1f2937;
        --text-muted: #6b7280;
        --border-color: #e5e7eb;

        /* Card-specific styles */
        --card-background: var(--background-secondary);
        --card-border-color: var(--border-color);
        --card-text-main: var(--text-main);
        --card-text-muted: var(--text-muted);
        --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1),
          0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --card-shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }

      @media (prefers-color-scheme: dark) {
        :root {
          /* Dark Mode Global */
          --background-main: #111827;
          --background-secondary: #1f2937;
          --text-main: #f9fafb;
          --text-muted: #9ca3af;
          --border-color: #374151;

          /* Card-specific styles for Dark Mode */
          --card-background: #1f2937; /* Slightly lighter than page to stand out */
          --card-border-color: #374151;
          --card-text-main: #f9fafb;
          --card-text-muted: #9ca3af;
        }
      }
    </style>

    <!-- Custom CSS -->
    <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />

    <!-- Additional page-specific styles -->
    {% block extra_css %}{% endblock %}
  </head>
  <body class="bg-gray-100 dark:bg-gray-900">
    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-md">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <!-- Logo and Main Nav -->
          <div class="flex items-center">
            <a
              href="{% if user.is_authenticated %}{% url 'dashboard' %}{% else %}{% url 'account_login' %}{% endif %}"
              class="flex-shrink-0 flex items-center space-x-2"
            >
              <span class="text-2xl">🎓</span>
              <span class="text-xl font-bold text-gray-800 dark:text-white"
                >{{ manual_texts.learn_english }}</span
              >
            </a>
            <div class="hidden md:block">
              <div class="ml-10 flex items-baseline space-x-4">
                {% if user.is_authenticated %}
                <a
                  href="{% url 'dashboard' %}"
                  class="{% if request.resolver_match.url_name == 'dashboard' %}bg-gray-900 text-white{% else %}text-gray-500 dark:text-gray-300 hover:bg-gray-700 hover:text-white{% endif %} px-3 py-2 rounded-md text-sm font-medium flex items-center space-x-1"
                  id="nav-home"
                >
                  <span class="nav-icon">🏠</span>
                  <span>{{ manual_texts.home }}</span>
                </a>
                <a
                  href="{% url 'deck_list' %}"
                  class="{% if request.resolver_match.url_name == 'deck_list' or request.resolver_match.url_name == 'deck_detail' %}bg-gray-900 text-white{% else %}text-gray-500 dark:text-gray-300 hover:bg-gray-700 hover:text-white{% endif %} px-3 py-2 rounded-md text-sm font-medium flex items-center space-x-1"
                  id="nav-flashcards"
                >
                  <span class="nav-icon">📚</span>
                  <span>{{ manual_texts.flashcards }}</span>
                </a>
                <a
                  href="{% url 'add_flashcard' %}"
                  class="{% if request.resolver_match.url_name == 'add_flashcard' %}bg-gray-900 text-white{% else %}text-gray-500 dark:text-gray-300 hover:bg-gray-700 hover:text-white{% endif %} px-3 py-2 rounded-md text-sm font-medium flex items-center space-x-1"
                  id="nav-add-word"
                >
                  <span class="nav-icon">➕</span>
                  <span>{{ manual_texts.add_word }}</span>
                </a>
                <a
                  href="{% url 'study' %}"
                  class="{% if request.resolver_match.url_name == 'study' %}bg-gray-900 text-white{% else %}text-gray-500 dark:text-gray-300 hover:bg-gray-700 hover:text-white{% endif %} px-3 py-2 rounded-md text-sm font-medium flex items-center space-x-1"
                  id="nav-study"
                >
                  <span class="nav-icon">📖</span>
                  <span>{{ manual_texts.study }}</span>
                </a>
                <a
                  href="{% url 'favorites' %}"
                  class="{% if request.resolver_match.url_name == 'favorites' %}bg-gray-900 text-white{% else %}text-gray-500 dark:text-gray-300 hover:bg-gray-700 hover:text-white{% endif %} px-3 py-2 rounded-md text-sm font-medium flex items-center space-x-1"
                  id="nav-favorites"
                >
                  <span class="nav-icon">❤️</span>
                  <span>{{ manual_texts.favorites|default:"Favorites" }}</span>
                  <span id="nav-favorites-count" class="ml-1 bg-red-500 text-white text-xs rounded-full px-2 py-1" style="display: none;">0</span>
                </a>
                <a
                  href="{% url 'statistics' %}"
                  class="{% if request.resolver_match.url_name == 'statistics' %}bg-gray-900 text-white{% else %}text-gray-500 dark:text-gray-300 hover:bg-gray-700 hover:text-white{% endif %} px-3 py-2 rounded-md text-sm font-medium flex items-center space-x-1"
                  id="nav-statistics"
                >
                  <span class="nav-icon">📊</span>
                  <span>{{ manual_texts.statistics }}</span>
                </a>
                {% endif %}
              </div>
            </div>
          </div>

          <!-- User and Language Menus -->
          <div class="hidden md:block">
            <div class="ml-4 flex items-center md:ml-6">
              {% if user.is_authenticated %}
              <!-- User Dropdown -->
              <div class="ml-3 relative">
                <div>
                  <button
                    type="button"
                    id="user-toggle"
                    class="max-w-xs bg-gray-800 rounded-full flex items-center text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white"
                    aria-expanded="false"
                    aria-haspopup="true"
                  >
                    <span class="sr-only">Open user menu</span>
                    <div
                      class="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center text-white font-bold"
                    >
                      {{ user.email|slice:":1"|upper }}
                    </div>
                  </button>
                </div>
                <div
                  id="user-menu"
                  class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 focus:outline-none hidden"
                  role="menu"
                  aria-orientation="vertical"
                  aria-labelledby="user-menu-button"
                  tabindex="-1"
                >
                  <div
                    class="px-4 py-3 border-b border-gray-200 dark:border-gray-600"
                  >
                    <p class="text-sm text-gray-900 dark:text-white">
                      {{ manual_texts.signed_in_as }}
                    </p>
                    <p
                      class="text-sm font-medium text-gray-900 dark:text-white truncate"
                    >
                      {{ user.email }}
                    </p>
                  </div>
                  <a
                    href="#"
                    class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"
                    role="menuitem"
                    tabindex="-1"
                    id="nav-profile"
                  >
                    <span>👤</span> {{ manual_texts.profile }}
                  </a>
                  <a
                    href="{% url 'account_logout' %}"
                    class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"
                    role="menuitem"
                    tabindex="-1"
                    id="nav-logout"
                  >
                    <span>🚪</span> {{ manual_texts.logout }}
                  </a>
                </div>
              </div>
              {% else %}
              <div class="flex items-center space-x-2">
                <a
                  href="{% url 'account_login' %}"
                  class="text-gray-500 dark:text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium"
                >
                  {{ manual_texts.sign_in }}
                </a>
                <a
                  href="{% url 'account_signup' %}"
                  class="bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-2 rounded-md text-sm font-medium"
                >
                  {{ manual_texts.sign_up }}
                </a>
              </div>
              {% endif %}

              <!-- Language Dropdown -->
              <div class="ml-3 relative">
                <div>
                  <button
                    type="button"
                    id="lang-toggle"
                    class="bg-white dark:bg-gray-800 p-1 rounded-full text-gray-400 dark:text-gray-300 hover:text-gray-500 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white"
                  >
                    <span class="sr-only">Switch language</span>
                    <span class="nav-icon">🌐</span>
                  </button>
                </div>
                <div
                  id="lang-menu"
                  class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 focus:outline-none hidden"
                  role="menu"
                  aria-orientation="vertical"
                  aria-labelledby="language-menu-button"
                  tabindex="-1"
                >
                  <form action="/i18n/setlang/" method="post" class="lang-form">
                    {% csrf_token %}
                    <input
                      name="next"
                      type="hidden"
                      value="{{ request.get_full_path }}"
                      id="nav-next-input"
                    />
                    <button
                      type="submit"
                      name="language"
                      value="en"
                      class="w-full text-left block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 {% if LANGUAGE_CODE == 'en' %}bg-gray-100 dark:bg-gray-600{% endif %}"
                      role="menuitem"
                      tabindex="-1"
                    >
                      🇺🇸 English
                    </button>
                    <button
                      type="submit"
                      name="language"
                      value="vi"
                      class="w-full text-left block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 {% if LANGUAGE_CODE == 'vi' %}bg-gray-100 dark:bg-gray-600{% endif %}"
                      role="menuitem"
                      tabindex="-1"
                    >
                      🇻🇳 Tiếng Việt
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>

          <!-- Mobile Menu Button -->
          <div class="-mr-2 flex md:hidden">
            <button
              type="button"
              id="mobile-menu-toggle"
              class="bg-gray-800 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white"
              aria-controls="mobile-menu"
              aria-expanded="false"
            >
              <span class="sr-only">Open main menu</span>
              <svg
                class="block h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
              <svg
                class="hidden h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Mobile Menu -->
      <div class="md:hidden hidden" id="mobile-menu">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
          {% if user.is_authenticated %}
          <a
            href="{% url 'dashboard' %}"
            class="{% if request.resolver_match.url_name == 'dashboard' %}bg-gray-900 text-white{% else %}text-gray-300 hover:bg-gray-700 hover:text-white{% endif %} block px-3 py-2 rounded-md text-base font-medium flex items-center space-x-2"
          >
            <span class="nav-icon">🏠</span>
            <span>{{ manual_texts.home }}</span>
          </a>
          <a
            href="{% url 'deck_list' %}"
            class="{% if request.resolver_match.url_name == 'deck_list' or request.resolver_match.url_name == 'deck_detail' %}bg-gray-900 text-white{% else %}text-gray-300 hover:bg-gray-700 hover:text-white{% endif %} block px-3 py-2 rounded-md text-base font-medium flex items-center space-x-2"
          >
            <span class="nav-icon">📚</span>
            <span>{{ manual_texts.flashcards }}</span>
          </a>
          <a
            href="{% url 'add_flashcard' %}"
            class="{% if request.resolver_match.url_name == 'add_flashcard' %}bg-gray-900 text-white{% else %}text-gray-300 hover:bg-gray-700 hover:text-white{% endif %} block px-3 py-2 rounded-md text-base font-medium flex items-center space-x-2"
          >
            <span class="nav-icon">➕</span>
            <span>{{ manual_texts.add_word }}</span>
          </a>
          <a
            href="{% url 'study' %}"
            class="{% if request.resolver_match.url_name == 'study' %}bg-gray-900 text-white{% else %}text-gray-300 hover:bg-gray-700 hover:text-white{% endif %} block px-3 py-2 rounded-md text-base font-medium flex items-center space-x-2"
          >
            <span class="nav-icon">📖</span>
            <span>{{ manual_texts.study }}</span>
          </a>
          <a
            href="{% url 'favorites' %}"
            class="{% if request.resolver_match.url_name == 'favorites' %}bg-gray-900 text-white{% else %}text-gray-300 hover:bg-gray-700 hover:text-white{% endif %} block px-3 py-2 rounded-md text-base font-medium flex items-center space-x-2"
          >
            <span class="nav-icon">❤️</span>
            <span>{{ manual_texts.favorites|default:"Favorites" }}</span>
            <span id="mobile-favorites-count" class="ml-1 bg-red-500 text-white text-xs rounded-full px-2 py-1" style="display: none;">0</span>
          </a>
          <a
            href="{% url 'statistics' %}"
            class="{% if request.resolver_match.url_name == 'statistics' %}bg-gray-900 text-white{% else %}text-gray-300 hover:bg-gray-700 hover:text-white{% endif %} block px-3 py-2 rounded-md text-base font-medium flex items-center space-x-2"
          >
            <span class="nav-icon">📊</span>
            <span>{{ manual_texts.statistics }}</span>
          </a>
          {% else %}
          <a
            href="{% url 'account_login' %}"
            class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium"
          >
            {{ manual_texts.sign_in }}
          </a>
          <a
            href="{% url 'account_signup' %}"
            class="bg-indigo-600 hover:bg-indigo-700 text-white block px-3 py-2 rounded-md text-base font-medium"
          >
            {{ manual_texts.sign_up }}
          </a>
          {% endif %}
        </div>
        {% if user.is_authenticated %}
        <div class="pt-4 pb-3 border-t border-gray-700">
          <div class="flex items-center px-5">
            <div class="flex-shrink-0">
              <div
                class="h-10 w-10 rounded-full bg-indigo-500 flex items-center justify-center text-white font-bold"
              >
                {{ user.email|slice:":1"|upper }}
              </div>
            </div>
            <div class="ml-3">
              <div class="text-base font-medium leading-none text-white">
                {{ user.first_name }} {{ user.last_name }}
              </div>
              <div class="text-sm font-medium leading-none text-gray-400">
                {{ user.email }}
              </div>
            </div>
          </div>
          <div class="mt-3 px-2 space-y-1">
            <a
              href="#"
              class="block px-3 py-2 rounded-md text-base font-medium text-gray-400 hover:text-white hover:bg-gray-700"
            >
              <span>👤</span> {{ manual_texts.profile }}
            </a>
            <a
              href="{% url 'account_logout' %}"
              class="block px-3 py-2 rounded-md text-base font-medium text-gray-400 hover:text-white hover:bg-gray-700"
            >
              <span>🚪</span> {{ manual_texts.logout }}
            </a>
          </div>
        </div>
        {% endif %}
      </div>
    </nav>
    <!-- Main Content -->
    <main class="py-10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {% block content %}{% endblock %}
      </div>
    </main>

    <!-- Expose localized texts to JavaScript -->
    <script>
      // Hybrid localization system - provides both legacy and modern i18n
      window.manual_texts = {{ js_translations_json|safe }};

      // Modern i18n functions (fallback if Django's JS catalog is not available)
      window.gettext = window.gettext || function(s) { return s; };
      window.ngettext = window.ngettext || function(s, p, n) { return n == 1 ? s : p; };
      window.interpolate = window.interpolate || function(s, o) { return s; };
    </script>

    <!-- Dictionary utilities (global) -->
    <script src="{% static 'js/dictionary-utils.js' %}"></script>

    <!-- Custom JavaScript -->
    <script src="{% static 'js/main.js' %}"></script>

    <!-- Additional page-specific scripts -->
    {% block extra_js %}{% endblock %}
  </body>
</html>
