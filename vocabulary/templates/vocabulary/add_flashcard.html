{% extends "base.html" %}

{% block title %}{{ manual_texts.add_flashcard }} - {{ manual_texts.learn_english }}{% endblock %}

{% block content %}
<meta name="csrf-token" content="{{ csrf_token }}" />
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<div class="add-flashcard-page">
  <div class="page-header">
    <h1>➕ {{ manual_texts.add_new_flashcard }}</h1>
    <p>{{ manual_texts.add_vocabulary_description }}</p>
  </div>

  <style>
    :root {
      --primary-color: #6a6cff;
      --primary-color-darker: #5a5ce0;
      --background-dark: #1a1a2e;
      --background-light: #24244a;
      --border-color: #3a3a5c;
      --text-main: #e0e0e0;
      --text-muted: #a0a0b8;
      --success-color: #4caf50;
      --error-color: #f44336;
    }

    .add-flashcard-page {
      max-width: 900px;
      margin: 20px auto;
      padding: 0 15px;
    }

    .page-header {
      text-align: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid var(--border-color);
    }

    .page-header h1 {
      font-size: 2.2em;
      font-weight: 700;
      margin-bottom: 8px;
      background: linear-gradient(135deg, var(--primary-color), #8e44ad);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .page-header p {
      color: var(--text-muted);
      font-size: 1.05em;
    }

    .deck-selection-area {
      background-color: var(--background-light);
      padding: 20px;
      border-radius: 12px;
      margin-bottom: 25px;
      display: flex;
      align-items: center;
      gap: 15px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }
    
    .deck-selection-area label {
        font-weight: 600;
        font-size: 1.1em;
        color: var(--text-main);
    }

    #deck-selector {
      flex-grow: 1;
      background-color: #1f1f3a;
      border: 1px solid #4a4a70;
      border-radius: 8px;
      padding: 12px;
      color: var(--text-main);
      font-size: 1em;
      transition: all 0.2s ease-in-out;
    }
    
    #deck-selector:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(106, 108, 255, 0.25);
    }

    .flashcard-container {
      margin-bottom: 20px;
    }

    .flashcard-section {
      background-color: var(--background-light);
      border: 1px solid var(--border-color);
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 20px;
      position: relative;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .flashcard-section:hover {
      border-color: var(--primary-color);
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(106, 108, 255, 0.15);
    }

    .flashcard-section.sortable-chosen {
      box-shadow: 0 4px 16px rgba(106, 108, 255, 0.3);
      transform: scale(1.01);
    }

    .flashcard-section.sortable-ghost {
      background: #2a2a50;
      opacity: 0.6;
    }

    .flashcard-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 18px;
      color: var(--text-main);
      font-size: 1.2em;
      font-weight: bold;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    .card-number {
      background: var(--primary-color);
      color: white;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.9em;
      font-weight: 600;
    }

    .actions {
      display: flex;
      gap: 8px;
    }

    .action-icon {
      background: transparent;
      border: 1px solid var(--border-color);
      color: var(--text-muted);
      font-size: 1.1em;
      cursor: pointer;
      padding: 6px;
      border-radius: 8px;
      transition: all 0.2s ease-in-out;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .action-icon:hover {
      color: var(--error-color);
      background: rgba(255, 82, 82, 0.1);
      border-color: var(--error-color);
    }

    .drag-handle:hover {
      color: var(--primary-color) !important;
      background: rgba(106, 108, 255, 0.1) !important;
      border-color: var(--primary-color) !important;
    }

    .drag-handle:active {
      cursor: grabbing;
    }

    .card-content-wrapper {
      display: flex;
      gap: 20px;
      align-items: flex-start;
    }
    
    .main-content-area {
        flex: 1;
        min-width: 0;
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto 1fr; /* Let first row be auto, second row expand */
        gap: 15px 20px;
        align-items: start;
    }

    .english-def-container,
    .vietnamese-block-container {
        display: flex;
        flex-direction: column;
        height: 100%; /* Make container fill grid cell height */
    }
    
    .english-def-container .input-field,
    .vietnamese-block-container .input-field {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }
    
    .english-def-container .definition-textarea,
    .vietnamese-block-container .vietnamese-textarea {
        flex-grow: 1; /* Make textarea fill available space */
    }

    .auto-info.inactive > * {
        opacity: 0.4;
        pointer-events: none;
    }

    .auto-info.inactive .auto-audio {
        cursor: not-allowed;
    }
    
    .image-upload-wrapper {
      /* Wrapper to contain the image upload box */
    }
    
    .details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        align-items: start;
        margin-top: 15px;
    }

    .input-group {
      display: grid;
      grid-template-columns: 1fr auto;
      gap: 15px;
      margin-bottom: 15px;
    }

    .input-field {
      display: flex;
      flex-direction: column;
      position: relative;
    }

    .input-field label {
      font-size: 0.8em;
      color: var(--text-muted);
      margin-bottom: 6px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.8px;
    }

    .input-field input[type="text"],
    .input-field textarea {
      background-color: #1f1f3a;
      border: 1px solid #4a4a70;
      border-radius: 8px;
      padding: 12px;
      color: var(--text-main);
      font-size: 1em;
      resize: vertical;
      min-height: 44px;
      transition: all 0.2s ease-in-out;
      line-height: 1.5;
    }
    
    .input-field textarea {
        min-height: unset;
    }
    
    .input-field input::placeholder,
    .input-field textarea::placeholder {
        color: #777;
    }

    .input-field input[type="text"]:focus,
    .input-field textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(106, 108, 255, 0.25);
    }
    
    .input-field input.correct-spelling {
      border-color: var(--success-color);
      color: var(--success-color);
    }
    
    .input-field input.incorrect-spelling {
       border-color: var(--error-color);
       color: var(--error-color);
    }

    .input-field input.duplicate-warning {
       border-color: #ff9800;
       color: #ff9800;
    }

    .image-upload {
      width: 140px;
      height: 140px;
      border: 2px dashed var(--border-color);
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      text-align: center;
      color: var(--text-muted);
      font-size: 0.85em;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      background-color: #2a2a50;
    }

    .image-upload:hover {
      border-color: var(--primary-color);
      background-color: #303058;
    }

    .image-upload img.preview {
      max-width: 100%;
      max-height: 100%;
      border-radius: 8px;
      position: absolute;
      top: 0;
      left: 0;
      object-fit: cover;
      width: 100%;
      height: 100%;
      z-index: 2;
    }

    .image-upload .image-upload-content {
      z-index: 3;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 5px;
      background: rgba(0,0,0,0.3);
      padding: 8px;
      border-radius: 8px;
    }
    
    .image-upload .image-upload-content svg {
      width: 28px;
      height: 28px;
      fill: var(--text-muted);
      transition: fill 0.3s ease;
    }
    
    .image-upload:hover .image-upload-content svg {
        fill: var(--primary-color);
    }

    .image-upload input[type="file"] {
      display: none;
    }

    .suggestions-list {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background-color: #2a2a50;
      border: 1px solid var(--primary-color);
      border-top: none;
      border-radius: 0 0 8px 8px;
      max-height: 200px;
      overflow-y: auto;
      z-index: 100;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    }

    .suggestion-item {
      padding: 10px 15px;
      cursor: pointer;
      color: var(--text-main);
      border-bottom: 1px solid var(--border-color);
      transition: background 0.2s ease;
      font-size: 0.95em;
    }

    .suggestion-item:last-child {
      border-bottom: none;
    }

    .suggestion-item:hover {
      background-color: var(--primary-color);
      color: #fff;
    }

    .duplicate-warning-item {
      background-color: rgba(255, 152, 0, 0.1);
      border-left: 3px solid #ff9800;
      color: #ff9800;
      padding: 10px 15px;
      margin: 5px 0;
      border-radius: 4px;
      font-size: 0.9em;
      cursor: default;
      border-bottom: 1px solid rgba(255, 152, 0, 0.3);
    }

    .duplicate-warning-item:hover {
      background-color: rgba(255, 152, 0, 0.15);
    }

    .button-group {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin: 25px 0;
    }

    .button {
      background: var(--primary-color);
      color: #ffffff;
      border: none;
      border-radius: 8px;
      padding: 12px 28px;
      font-size: 1em;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
      box-shadow: 0 4px 12px rgba(106, 108, 255, 0.2);
    }

    .button:hover {
      transform: translateY(-2px);
      background: var(--primary-color-darker);
      box-shadow: 0 6px 18px rgba(106, 108, 255, 0.3);
    }

    .save-section {
      text-align: center;
      margin-top: 25px;
      padding-top: 25px;
      border-top: 1px solid var(--border-color);
    }

    .save-button {
      background: linear-gradient(135deg, #4caf50, #66bb6a);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 14px 36px;
      font-size: 1.1em;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
      box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    }

    .save-button:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 30px rgba(76, 175, 80, 0.4);
    }

    .auto-info {
      margin-top: 10px;
      display: flex;
      align-items: center;
      gap: 15px;
      padding: 8px 12px;
      background: rgba(106, 108, 255, 0.1);
      border-radius: 8px;
      border-left: 3px solid var(--primary-color);
      font-size: 0.9em;
    }

    .auto-pos {
      color: var(--primary-color);
      font-weight: 600;
      font-style: italic;
    }

    .auto-audio {
      color: #c0c0ff;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 5px 10px;
      border-radius: 20px;
      transition: all 0.3s ease;
      font-weight: 500;
    }

    .auto-audio:hover {
      background: var(--primary-color);
      color: #fff;
    }
    
    .auto-audio svg {
      transition: transform 0.2s ease;
    }
    
    .auto-audio:hover svg {
      transform: scale(1.1);
    }

    /* Definition Suggestions Styling */
    .definition-suggestion-item {
      padding: 12px 15px;
      cursor: pointer;
      color: var(--text-main);
      border-bottom: 1px solid var(--border-color);
      transition: background 0.2s ease;
      line-height: 1.5;
    }

    .definition-suggestion-item:last-child {
      border-bottom: none;
    }

    .definition-suggestion-item:hover {
      background-color: var(--primary-color);
      color: #fff;
    }

    /* Quick Add Words Section */
    .quick-add-section {
      background-color: var(--background-light);
      border: 1px solid var(--border-color);
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 25px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .quick-add-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 15px;
    }

    .quick-add-header h3 {
      margin: 0;
      color: var(--text-main);
      font-size: 1.2em;
      font-weight: 600;
    }

    .quick-add-icon {
      color: var(--primary-color);
      font-size: 1.3em;
    }

    .quick-add-input {
      width: 100%;
      min-height: 80px;
      background-color: #1f1f3a;
      border: 1px solid #4a4a70;
      border-radius: 8px;
      padding: 12px;
      color: var(--text-main);
      font-size: 0.95em;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      resize: vertical;
      transition: all 0.2s ease-in-out;
      margin-bottom: 15px;
    }

    .quick-add-input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(106, 108, 255, 0.25);
    }

    .quick-add-input::placeholder {
      color: var(--text-muted);
      font-style: italic;
    }

    .quick-add-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 15px;
    }

    .quick-add-info {
      color: var(--text-muted);
      font-size: 0.9em;
      flex: 1;
    }

    .generate-cards-btn {
      background: linear-gradient(135deg, var(--primary-color), #8e44ad);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 10px 20px;
      font-size: 0.95em;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .generate-cards-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(106, 108, 255, 0.3);
    }

    .generate-cards-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .processing-indicator {
      display: none;
      align-items: center;
      gap: 10px;
      color: var(--primary-color);
      font-size: 0.9em;
      margin-top: 10px;
    }

    .processing-indicator.active {
      display: flex;
    }

    .spinner {
      width: 16px;
      height: 16px;
      border: 2px solid rgba(106, 108, 255, 0.3);
      border-top: 2px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @media (max-width: 820px) {
      .card-content-wrapper {
        flex-direction: column-reverse;
        gap: 15px;
      }
      .main-content-area {
        grid-template-columns: 1fr;
      }
      .details-grid {
        grid-template-columns: 1fr;
      }
      .input-group {
        grid-template-columns: 1fr;
        gap: 15px;
      }

      .image-upload {
        width: 100%;
        height: 140px;
      }

      .quick-add-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
      }

      .quick-add-info {
        text-align: center;
      }
    }
  </style>

  <div class="deck-selection-area">
    <label for="deck-selector">{{ manual_texts.select_deck }}</label>
    <select id="deck-selector" name="deck">
      <option value="">{{ manual_texts.please_select_deck }}</option>
      <option value="new_deck">{{ manual_texts.create_new_deck }}</option>
      {% for deck in decks %}
        <option value="{{ deck.id }}">{{ deck.name }}</option>
      {% endfor %}
    </select>
  </div>

  <!-- Quick Add Words Section -->
  <div class="quick-add-section">
    <div class="quick-add-header">
      <span class="quick-add-icon">⚡</span>
      <h3>{{ manual_texts.quick_add_multiple_words }}</h3>
    </div>
    <textarea
      id="quick-add-input"
      class="quick-add-input"
      placeholder="{{ manual_texts.quick_add_placeholder }}"
      rows="3"></textarea>
    <div class="quick-add-controls">
      <div class="quick-add-info">
        {{ manual_texts.quick_add_info }}
      </div>
      <button id="generate-cards-btn" class="generate-cards-btn">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
        </svg>
        {{ manual_texts.generate_cards }}
      </button>
    </div>
    <div id="processing-indicator" class="processing-indicator">
      <div class="spinner"></div>
      <span id="processing-text">{{ manual_texts.processing_words }}</span>
    </div>
  </div>

  <div class="flashcard-container" id="flashcard-container"
       data-check-spelling-url="{% url 'check_spelling' %}"
       data-suggest-words-url="{% url 'suggest_words' %}"
       data-word-details-url="{% url 'get_word_details_api' %}"
       data-translate-url="{% url 'translate_to_vietnamese' %}"
       data-check-word-exists-url="{% url 'check_word_exists' %}">
    <div class="flashcard-section" data-card-index="1">
      <div class="flashcard-header">
        <span class="card-number">1</span>
        <div class="actions">
          <button class="action-icon drag-handle" title="{{ manual_texts.drag_to_move }}">
            <svg
              width="22"
              height="22"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              style="vertical-align: middle"
            >
              <line x1="3" y1="6" x2="21" y2="6" />
              <line x1="3" y1="12" x2="21" y2="12" />
              <line x1="3" y1="18" x2="21" y2="18" />
            </svg>
          </button>
          <button class="action-icon delete-card-btn" title="{{ manual_texts.delete_card }}">
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              style="vertical-align: middle"
            >
              <polyline points="3 6 5 6 21 6" />
              <path
                d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2"
              />
              <line x1="10" y1="11" x2="10" y2="17" />
              <line x1="14" y1="11" x2="14" y2="17" />
            </svg>
          </button>
        </div>
      </div>
      <div class="card-content-wrapper">
        <div class="main-content-area">
            <!-- Term -->
            <div class="input-field">
              <label for="term1">{{ manual_texts.term_label }}</label>
              <input type="text" id="term1" name="term1" class="term-input" placeholder="{{ manual_texts.term_placeholder }}" />
              <div
                id="suggestions1"
                class="suggestions-list"
                style="display: none"
              ></div>
            </div>

            <!-- Phonetic -->
            <div class="input-field">
              <label for="phonetic1">{{ manual_texts.phonetic_label }}</label>
              <input type="text" id="phonetic1" name="phonetic1" class="phonetic-input" placeholder="{{ manual_texts.phonetic_placeholder }}" readonly style="background-color: #1f1f3a; color: #a0a0b8; cursor: not-allowed;" />
            </div>

            <!-- English Definition -->
            <div class="english-def-container">
                <div class="input-field">
                    <label for="definition1">{{ manual_texts.english_definition_label }}</label>
                    <textarea id="definition1" name="definition1" class="definition-textarea" rows="4" placeholder="{{ manual_texts.definition_placeholder }}"></textarea>
                    <div class="definition-suggestions suggestions-list" style="display: none;"></div>
                </div>
            </div>

            <!-- Vietnamese Block -->
            <div class="vietnamese-block-container">
                <div class="auto-info inactive" style="margin-bottom: 10px;">
                    <span class="auto-pos">{{ manual_texts.part_of_speech }}</span>
                    <span class="auto-audio">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"></path></svg>
                        <span>{{ manual_texts.listen }}</span>
                    </span>
                </div>
                <div class="input-field">
                    <label for="vietnamese_definition1">{{ manual_texts.vietnamese_definition_label }}</label>
                    <textarea
                        id="vietnamese_definition1"
                        name="vietnamese_definition1"
                        class="vietnamese-textarea"
                        rows="3"
                        placeholder="{{ manual_texts.vietnamese_placeholder }}"
                    ></textarea>
                </div>
            </div>
        </div>

        <div class="image-upload-wrapper">
            <div class="image-upload" id="imageUpload1">
              <input type="file" accept="image/*" id="fileInput1" />
              <img class="preview" id="imgPreview1" style="display: none" />
              <button
                type="button"
                id="deleteImg1"
                class="delete-img-btn"
                style="
                  position: absolute;
                  top: 5px;
                  right: 5px;
                  z-index: 4;
                  background: rgba(0, 0, 0, 0.5);
                  border: none;
                  border-radius: 50%;
                  color: #fff;
                  font-size: 18px;
                  cursor: pointer;
                  width: 24px;
                  height: 24px;
                  display: none;
                  align-items: center;
                  justify-content: center;
                  line-height: 20px;
                  padding: 0;
                "
              >
                &times;
              </button>
              <div class="image-upload-content">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM14 13v4h-4v-4H7l5-5 5 5h-3z" />
                </svg>
                <span>{{ manual_texts.upload_image }}</span>
              </div>
            </div>
        </div>
      </div>
    </div>
  </div>
  <div class="button-group">
    <button id="add-card-btn" class="button">{{ manual_texts.add_new_card }}</button>
  </div>
  <div class="save-section">
    <button id="save-all-btn" class="save-button">
      {{ manual_texts.save_all_flashcards }}
    </button>
  </div>



  <script>
document.addEventListener('DOMContentLoaded', function() {
    const flashcardContainer = document.getElementById('flashcard-container');
    // URLs from data attributes
    const checkSpellingUrl = flashcardContainer.dataset.checkSpellingUrl;
    const suggestWordsUrl = flashcardContainer.dataset.suggestWordsUrl;
    const wordDetailsUrl = flashcardContainer.dataset.wordDetailsUrl;
    const translateUrl = flashcardContainer.dataset.translateUrl;
    const checkWordExistsUrl = flashcardContainer.dataset.checkWordExistsUrl;
    
    const addCardBtn = document.getElementById('add-card-btn');
    const saveAllBtn = document.getElementById('save-all-btn');
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    let cardCount = 1;
    let wordApiCache = {};

    const deckSelector = document.getElementById('deck-selector');

    // Style the "Create new deck" option to make it more prominent
    function styleCreateNewDeckOption() {
        const createNewDeckOption = deckSelector.querySelector('option[value="new_deck"]');
        if (createNewDeckOption) {
            createNewDeckOption.style.fontWeight = 'bold';
            createNewDeckOption.style.color = '#6a6cff';
            // Add a visual indicator
            if (!createNewDeckOption.textContent.includes('✨')) {
                createNewDeckOption.textContent = '✨ ' + createNewDeckOption.textContent;
            }
        }
    }

    // Apply styling after DOM is ready
    styleCreateNewDeckOption();

    // Quick Add elements
    const quickAddInput = document.getElementById('quick-add-input');
    const generateCardsBtn = document.getElementById('generate-cards-btn');
    const processingIndicator = document.getElementById('processing-indicator');
    const processingText = document.getElementById('processing-text');

    deckSelector.addEventListener('change', async function() {
        if (this.value === 'new_deck') {
            const { value: deckName } = await Swal.fire({
                title: '{{ manual_texts.create_new_deck_title }}',
                input: 'text',
                inputLabel: '{{ manual_texts.deck_name_label }}',
                inputPlaceholder: '{{ manual_texts.deck_name_placeholder }}',
                background: 'var(--background-light)',
                color: 'var(--text-main)',
                confirmButtonColor: 'var(--primary-color)',
                inputValidator: (value) => {
                    if (!value) {
                        return '{{ manual_texts.deck_name_required }}'
                    }
                },
                showCancelButton: true,
                cancelButtonText: '{{ manual_texts.cancel }}'
            });

            if (deckName) {
                try {
                    const response = await fetch("{% url 'create_deck_api' %}", {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': getCSRFToken()
                        },
                        body: JSON.stringify({ name: deckName })
                    });
                    const data = await response.json();
                    if (data.success) {
                        const newOption = new Option(data.deck.name, data.deck.id, true, true);
                        // Insert after the "Create new deck" option (which is now at index 1)
                        deckSelector.add(newOption, deckSelector.options[2]);
                        Swal.fire({
                            icon: 'success',
                            title: '{{ manual_texts.created }}',
                            text: `{{ manual_texts.deck_created_success }}`.replace('{deck_name}', data.deck.name),
                            background: 'var(--background-light)',
                            color: 'var(--text-main)',
                            confirmButtonColor: 'var(--primary-color)'
                        });
                    } else {
                        throw new Error(data.error || '{{ manual_texts.unknown_error }}');
                    }
                } catch (error) {
                    Swal.fire({
                        icon: 'error',
                        title: '{{ manual_texts.cannot_create_deck }}',
                        text: error.message,
                        background: 'var(--background-light)',
                        color: 'var(--text-main)',
                        confirmButtonColor: 'var(--primary-color)'
                    });
                    deckSelector.value = ''; // Reset selection
                }
            } else {
                deckSelector.value = ''; // Reset selection if user cancels
            }
        }
    });

    // =============================================
    // UTILITY FUNCTIONS
    // =============================================
    function getCSRFToken() {
        return document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    }

    function debounce(func, delay) {
        let timeout;
        return function(...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), delay);
        };
    }

    // =============================================
    // CORE LOGIC: SPELLING, SUGGESTIONS, DETAILS
    // =============================================

    // 1. Check for Duplicate Words
    function checkDuplicates(word, input, suggestionsList, card) {
        fetch(`${checkWordExistsUrl}?word=${encodeURIComponent(word)}`)
        .then(res => res.ok ? res.json() : Promise.reject('Network error'))
        .then(data => {
            if (data.exists) {
                // Word already exists - show duplicate warning
                input.classList.add("duplicate-warning");
                showDuplicateWarning(suggestionsList, word);
            } else {
                // Word doesn't exist - remove duplicate warning and proceed normally
                input.classList.remove("duplicate-warning");
                fetchWordDetailsAndCache(word, card);
            }
        })
        .catch(err => {
            console.error("Duplicate check error:", err);
            // On error, proceed normally without duplicate warning
            input.classList.remove("duplicate-warning");
            fetchWordDetailsAndCache(word, card);
        });
    }

    // Show duplicate warning in suggestions list
    function showDuplicateWarning(suggestionsList, word) {
        suggestionsList.innerHTML = "";
        const warningItem = document.createElement("div");
        warningItem.className = "duplicate-warning-item";
        warningItem.innerHTML = `
            <strong>⚠️ {{ manual_texts.duplicate_word_detected }}</strong><br>
            {{ manual_texts.word_already_exists }}`.replace('{word}', `<strong>${word}</strong>`) + `<br>
            {{ manual_texts.use_different_word }}
        `;
        suggestionsList.appendChild(warningItem);
        suggestionsList.style.display = "block";
    }

    // 2. Check Spelling
    function checkSpelling(input, suggestionsList, card) {
        const word = input.value.trim();
        if (word.length < 2) {
            suggestionsList.style.display = "none";
            input.classList.remove("correct-spelling", "incorrect-spelling", "duplicate-warning");
            return;
        }

        fetch(checkSpellingUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
                "X-CSRFToken": getCSRFToken(),
            },
            body: `word=${encodeURIComponent(word)}`,
        })
        .then(res => res.ok ? res.json() : Promise.reject('Network error'))
        .then(data => {
            const isCorrect = data.is_correct;
            input.classList.toggle("correct-spelling", isCorrect);
            input.classList.toggle("incorrect-spelling", !isCorrect);

            if (isCorrect) {
                suggestionsList.innerHTML = "";
                suggestionsList.style.display = "none";
                // Check for duplicates after confirming correct spelling
                checkDuplicates(word, input, suggestionsList, card);
            } else {
                // Remove duplicate warning for incorrect spelling
                input.classList.remove("duplicate-warning");
                fetchSuggestions(word, suggestionsList, card);
            }
        })
        .catch(err => {
            console.error("Spelling check error:", err);
            input.classList.remove("correct-spelling", "incorrect-spelling", "duplicate-warning");
            suggestionsList.style.display = "none";
        });
    }

    // 2. Fetch Word Suggestions (for incorrect spelling)
    function fetchSuggestions(query, suggestionsList, card) {
        fetch(`${suggestWordsUrl}?q=${encodeURIComponent(query)}`)
        .then(res => res.ok ? res.json() : Promise.reject('Network error'))
        .then(data => {
            suggestionsList.innerHTML = "";
            if (data && data.length > 0) {
                data.forEach(word => {
                    const item = document.createElement("div");
                    item.className = "suggestion-item";
                    item.textContent = word;
                    item.addEventListener("click", () => {
                        const termInput = card.querySelector('.term-input');
                        termInput.value = word;
                        suggestionsList.style.display = "none";
                        checkSpelling(termInput, suggestionsList, card); 
                    });
                    suggestionsList.appendChild(item);
                });
                suggestionsList.style.display = "block";
            } else {
                suggestionsList.style.display = "none";
            }
        })
        .catch(err => {
            console.error('Suggestion fetch error:', err);
            suggestionsList.style.display = "none";
        });
    }

    // 3. Fetch Word Details (for correct spelling) and update UI
    function fetchWordDetailsAndCache(word, card) {
        if (!word) return;
        const lowerCaseWord = word.toLowerCase();

        // Use cache if available
        if (wordApiCache[lowerCaseWord]) {
            updateCardUI(card, wordApiCache[lowerCaseWord]);
            return;
        }

        // Fetch from API if not in cache
        fetch(`${wordDetailsUrl}?word=${encodeURIComponent(word)}`)
        .then(res => res.ok ? res.json() : Promise.reject('API error'))
        .then(data => {
            if (data && !data.error) {
                wordApiCache[lowerCaseWord] = data; // Cache the result
                updateCardUI(card, data);
            } else {
                console.error("Word details error:", data.error);
                resetCardUI(card);
            }
        })
        .catch(err => {
            console.error("Fetch details error:", err);
            resetCardUI(card);
        });
    }

    // =============================================
    // UI UPDATE FUNCTIONS
    // =============================================

    function updateCardUI(card, data) {
        const phoneticInput = card.querySelector(".phonetic-input");
        const autoInfo = card.querySelector(".auto-info");
        const autoPos = card.querySelector(".auto-pos");
        const autoAudio = card.querySelector(".auto-audio");
        const definitionTextarea = card.querySelector(".definition-textarea");
        const vietnameseTextarea = card.querySelector(".vietnamese-textarea");
        const definitionSuggestions = card.querySelector('.definition-suggestions');

        // Reset previous state first
        resetCardUI(card);

        // Phonetic
        const phoneticText = data.phonetics?.find(p => p.text)?.text || "";
        phoneticInput.value = phoneticText;

        if (autoInfo) {
            autoInfo.style.display = 'flex';
            autoInfo.classList.remove('inactive');
        }

        // Part of Speech
        const partOfSpeech = data.meanings?.[0]?.part_of_speech || "";
        if (autoPos) autoPos.textContent = partOfSpeech;

        // Audio
        const audioUrl = data.phonetics?.find(p => p.audio)?.audio || "";
        if (autoAudio) {
            autoAudio.dataset.audioUrl = audioUrl;
            autoAudio.innerHTML = audioUrl ? `
                <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"></path></svg>
                <span>{{ manual_texts.listen }}</span>` : "";
        }
        if (autoInfo) autoInfo.style.display = "flex";

        // Handle Definitions
        definitionSuggestions.innerHTML = '';
        const allDefinitions = data.meanings?.flatMap(m => m.definitions.map(d => ({ ...d, part_of_speech: m.part_of_speech }))) || [];
        
        if (allDefinitions.length > 0) {
            // Populate suggestions list
            allDefinitions.forEach(def => {
                const item = document.createElement('div');
                item.className = 'suggestion-item';
                item.innerHTML = `<span style="color:#b0b0ff;font-style:italic;">(${def.part_of_speech})</span> ${def.en}`;
                item.addEventListener('click', (e) => {
                    e.stopPropagation();
                    definitionTextarea.value = def.en;
                    definitionSuggestions.style.display = 'none';
                    // No need to re-translate, as the core term hasn't changed.
                    // The initial translation is sufficient.
                });
                definitionSuggestions.appendChild(item);
            });

            // Set up show/hide events for the suggestion box
            definitionTextarea.addEventListener('focus', () => {
                if(definitionSuggestions.innerHTML !== ''){
                   definitionSuggestions.style.display = 'block';
                }
            });
            definitionTextarea.addEventListener('blur', () => {
                setTimeout(() => {
                    definitionSuggestions.style.display = 'none';
                }, 200); // Delay to allow click on suggestion
            });
            
            // Auto-fill the first definition and then translate the *term*
            const firstDefinition = allDefinitions[0].en;
            const termToTranslate = card.querySelector('.term-input').value.trim();
            definitionTextarea.value = firstDefinition;
            translateToVietnamese(termToTranslate, vietnameseTextarea);

        } else {
            // No definitions found
            definitionTextarea.value = '';
            vietnameseTextarea.value = '';
        }
    }
    
    function resetCardUI(card) {
        const phoneticInput = card.querySelector(".phonetic-input");
        if (phoneticInput) phoneticInput.value = "";

        const autoInfo = card.querySelector(".auto-info");
        if (autoInfo) {
            autoInfo.classList.add('inactive');
            const autoPos = card.querySelector(".auto-pos");
            if (autoPos) autoPos.textContent = "{{ manual_texts.part_of_speech }}";
            const autoAudio = card.querySelector(".auto-audio");
            if (autoAudio) {
                autoAudio.dataset.audioUrl = "";
                autoAudio.innerHTML = `
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"></path></svg>
                    <span>{{ manual_texts.listen }}</span>`;
            }
        }

        const definitionTextarea = card.querySelector(".definition-textarea");
        if (definitionTextarea) definitionTextarea.value = "";

        const vietnameseTextarea = card.querySelector(".vietnamese-textarea");
        if (vietnameseTextarea) vietnameseTextarea.value = "";
        
        const definitionSuggestions = card.querySelector('.definition-suggestions');
        if (definitionSuggestions) {
            definitionSuggestions.innerHTML = "";
            definitionSuggestions.style.display = "none";
        }
    }

    function translateToVietnamese(text, targetTextarea) {
        if (!text) {
            targetTextarea.value = "";
            return;
        }
        // Show loading state
        targetTextarea.value = "{{ manual_texts.translating }}";

        fetch(`${translateUrl}?text=${encodeURIComponent(text)}`)
        .then(res => {
            if (!res.ok) {
                return res.json().then(err => Promise.reject(err));
            }
            return res.json();
        })
        .then(data => {
            if(data.translated_text) {
                targetTextarea.value = data.translated_text;
            } else {
                targetTextarea.value = "{{ manual_texts.translation_not_available }}";
            }
        })
        .catch(err => {
            console.error('Translation error:', err);
            targetTextarea.value = "{{ manual_texts.translation_error }}";
        });
    }

    // =============================================
    // CARD MANAGEMENT (ADD, DELETE, SORT)
    // =============================================

    function createNewCard() {
        cardCount++;
        const template = document.querySelector('.flashcard-section');
        const newCard = template.cloneNode(true);
        newCard.dataset.cardIndex = cardCount;
        newCard.querySelector('.card-number').textContent = cardCount;

        // Reset all input values
        newCard.querySelectorAll('input, textarea').forEach(input => {
            input.value = '';
            input.placeholder = input.placeholder; // Keep placeholder
        });
        newCard.querySelectorAll('.suggestions-list, .definition-suggestions').forEach(el => {
            el.innerHTML = '';
            el.style.display = 'none';
        });
        
        // Reset UI state
        newCard.querySelector('.term-input').classList.remove('correct-spelling', 'incorrect-spelling', 'duplicate-warning');
        resetCardUI(newCard);
        
        // Update IDs to be unique
        newCard.querySelectorAll('[id]').forEach(el => {
            el.id = el.id.replace(/\d+$/, '') + cardCount;
        });
        newCard.querySelectorAll('[for]').forEach(el => {
            el.htmlFor = el.htmlFor.replace(/\d+$/, '') + cardCount;
        });
        // Remove image preview from cloned card
        const imgPreview = newCard.querySelector('.preview');
        const deleteImgBtn = newCard.querySelector('.delete-img-btn');
        imgPreview.src = '';
        imgPreview.style.display = 'none';
        deleteImgBtn.style.display = 'none';

        flashcardContainer.appendChild(newCard);
        initializeCard(newCard);
        updateCardNumbers(); // Update numbers after adding
    }
    
    function deleteCard(cardElement) {
        if (flashcardContainer.children.length > 1) {
            cardElement.remove();
            updateCardNumbers();
        } else {
            alert("{{ manual_texts.cannot_delete_only_card }}");
        }
    }

    function updateCardNumbers() {
        const cards = flashcardContainer.querySelectorAll('.flashcard-section');
        cards.forEach((card, index) => {
            card.querySelector('.card-number').textContent = index + 1;
        });
    }

    // =============================================
    // QUICK ADD WORDS FUNCTIONALITY
    // =============================================

    function parseQuickAddInput(input) {
        if (!input || typeof input !== 'string') return [];

        return input
            .split('|')
            .map(word => word.trim())
            .filter(word => word.length > 0)
            .filter(word => word.length <= 255) // Respect database field limit
            .filter((word, index, arr) => arr.indexOf(word.toLowerCase()) === arr.findIndex(w => w.toLowerCase() === word.toLowerCase())); // Remove case-insensitive duplicates
    }

    function showProcessingIndicator(show, text = 'Processing words...') {
        if (show) {
            processingText.textContent = text;
            processingIndicator.classList.add('active');
            generateCardsBtn.disabled = true;
        } else {
            processingIndicator.classList.remove('active');
            generateCardsBtn.disabled = false;
        }
    }

    function clearAllCardsExceptFirst() {
        const allCards = flashcardContainer.querySelectorAll('.flashcard-section');

        // Remove all cards except the first one
        allCards.forEach((card, index) => {
            if (index > 0) {
                card.remove();
            }
        });

        // Reset the first card to empty state
        const firstCard = flashcardContainer.querySelector('.flashcard-section');
        if (firstCard) {
            // Clear all input values
            firstCard.querySelectorAll('input, textarea').forEach(input => {
                input.value = '';
            });

            // Clear suggestions and hide them
            firstCard.querySelectorAll('.suggestions-list, .definition-suggestions').forEach(el => {
                el.innerHTML = '';
                el.style.display = 'none';
            });

            // Reset CSS classes
            firstCard.querySelector('.term-input').classList.remove('correct-spelling', 'incorrect-spelling', 'duplicate-warning');

            // Reset card UI
            resetCardUI(firstCard);

            // Reset image preview
            const imgPreview = firstCard.querySelector('.preview');
            const deleteImgBtn = firstCard.querySelector('.delete-img-btn');
            if (imgPreview) {
                imgPreview.src = '';
                imgPreview.style.display = 'none';
            }
            if (deleteImgBtn) {
                deleteImgBtn.style.display = 'none';
            }

            // Reset card number to 1
            firstCard.querySelector('.card-number').textContent = '1';
            firstCard.dataset.cardIndex = '1';
        }

        // Reset card counter
        cardCount = 1;

        // Update card numbers (should just be the first card now)
        updateCardNumbers();
    }

    async function generateCardsFromWords(words) {
        if (!words || words.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: '{{ manual_texts.no_words_found }}',
                text: '{{ manual_texts.enter_words_pipe }}',
                background: 'var(--background-light)',
                color: 'var(--text-main)',
                confirmButtonColor: 'var(--primary-color)'
            });
            return;
        }

        // Check if a deck is selected
        const selectedDeckId = deckSelector.value;
        if (!selectedDeckId || selectedDeckId === 'new_deck') {
            Swal.fire({
                icon: 'warning',
                title: '{{ manual_texts.no_deck_selected }}',
                text: '{{ manual_texts.select_deck_before_adding }}',
                background: 'var(--background-light)',
                color: 'var(--text-main)',
                confirmButtonColor: 'var(--primary-color)'
            });
            return;
        }

        showProcessingIndicator(true, '{{ manual_texts.processing_words }}');

        // Clear all existing cards first, keeping one empty card as template
        clearAllCardsExceptFirst();

        showProcessingIndicator(true, `{{ manual_texts.processing_words }} (${words.length})`);

        const results = {
            successful: [],
            duplicates: [],
            errors: []
        };

        try {
            for (let i = 0; i < words.length; i++) {
                const word = words[i];
                showProcessingIndicator(true, `{{ manual_texts.processing_word_individual }}`.replace('{word}', word).replace('{current}', i + 1).replace('{total}', words.length));

                try {
                    // Check for duplicates first
                    const duplicateCheck = await fetch(`${checkWordExistsUrl}?word=${encodeURIComponent(word)}`);
                    const duplicateData = await duplicateCheck.json();

                    if (duplicateData.exists) {
                        results.duplicates.push(word);
                        continue;
                    }

                    // Use first empty card for first word, create new cards for subsequent words
                    let targetCard;
                    if (i === 0) {
                        // Use the existing first card for the first word
                        targetCard = flashcardContainer.querySelector('.flashcard-section');
                        targetCard.querySelector('.term-input').value = word;
                    } else {
                        // Create new card for subsequent words
                        targetCard = createNewCardForWord(word);
                    }

                    if (targetCard) {
                        results.successful.push(word);

                        // Trigger spell check and word details fetching
                        const termInput = targetCard.querySelector('.term-input');
                        const suggestionsList = targetCard.querySelector('.suggestions-list');

                        // Small delay to allow DOM to settle
                        await new Promise(resolve => setTimeout(resolve, 100));

                        // Trigger the existing spell check functionality
                        checkSpelling(termInput, suggestionsList, targetCard);
                    }
                } catch (error) {
                    console.error(`Error processing word "${word}":`, error);
                    results.errors.push(word);
                }
            }

            showProcessingIndicator(false);
            showQuickAddResults(results);

            // Clear the input if successful
            if (results.successful.length > 0) {
                quickAddInput.value = '';
            }

        } catch (error) {
            console.error('Error in generateCardsFromWords:', error);
            showProcessingIndicator(false);
            Swal.fire({
                icon: 'error',
                title: 'Processing Error',
                text: 'An error occurred while processing the words. Please try again.',
                background: 'var(--background-light)',
                color: 'var(--text-main)',
                confirmButtonColor: 'var(--primary-color)'
            });
        }
    }

    function createNewCardForWord(word) {
        try {
            cardCount++;
            const template = document.querySelector('.flashcard-section');
            const newCard = template.cloneNode(true);
            newCard.dataset.cardIndex = cardCount;
            newCard.querySelector('.card-number').textContent = cardCount;

            // Reset all input values
            newCard.querySelectorAll('input, textarea').forEach(input => {
                input.value = '';
                input.placeholder = input.placeholder; // Keep placeholder
            });
            newCard.querySelectorAll('.suggestions-list, .definition-suggestions').forEach(el => {
                el.innerHTML = '';
                el.style.display = 'none';
            });

            // Reset UI state
            newCard.querySelector('.term-input').classList.remove('correct-spelling', 'incorrect-spelling', 'duplicate-warning');
            resetCardUI(newCard);

            // Update IDs to be unique
            newCard.querySelectorAll('[id]').forEach(el => {
                el.id = el.id.replace(/\d+$/, '') + cardCount;
            });
            newCard.querySelectorAll('[for]').forEach(el => {
                el.htmlFor = el.htmlFor.replace(/\d+$/, '') + cardCount;
            });

            // Remove image preview from cloned card
            const imgPreview = newCard.querySelector('.preview');
            const deleteImgBtn = newCard.querySelector('.delete-img-btn');
            imgPreview.src = '';
            imgPreview.style.display = 'none';
            deleteImgBtn.style.display = 'none';

            // Set the word in the term input
            newCard.querySelector('.term-input').value = word;

            flashcardContainer.appendChild(newCard);
            initializeCard(newCard);
            updateCardNumbers(); // Update numbers after adding

            return newCard;
        } catch (error) {
            console.error('Error creating card for word:', word, error);
            return null;
        }
    }

    function showQuickAddResults(results) {
        let message = '';
        let icon = 'success';

        if (results.successful.length > 0) {
            message += `✅ {{ manual_texts.words_added_successfully }}`.replace('{count}', results.successful.length).replace('{words}', results.successful.join(', ')) + '\n\n';
        }

        if (results.duplicates.length > 0) {
            message += `⚠️ {{ manual_texts.duplicate_words_skipped }}`.replace('{count}', results.duplicates.length).replace('{words}', results.duplicates.join(', ')) + '\n\n';
            icon = results.successful.length > 0 ? 'warning' : 'warning';
        }

        if (results.errors.length > 0) {
            message += `❌ {{ manual_texts.words_with_errors }}`.replace('{count}', results.errors.length).replace('{words}', results.errors.join(', ')) + '\n\n';
            icon = results.successful.length > 0 ? 'warning' : 'error';
        }

        if (results.successful.length === 0 && results.duplicates.length === 0 && results.errors.length === 0) {
            message = '{{ manual_texts.no_words_processed }}';
            icon = 'warning';
        }

        Swal.fire({
            icon: icon,
            title: '{{ manual_texts.quick_add_results }}',
            html: message.replace(/\n/g, '<br>'),
            background: 'var(--background-light)',
            color: 'var(--text-main)',
            confirmButtonColor: 'var(--primary-color)',
            width: '600px'
        });
    }

    // =============================================
    // INITIALIZATION
    // =============================================

    function initializeCard(card) {
        const termInput = card.querySelector('.term-input');
        const suggestionsList = card.querySelector('.suggestions-list');
        const autoAudio = card.querySelector('.auto-audio');
        const deleteBtn = card.querySelector('.delete-card-btn');
        
        // Debounced spelling check
        termInput.addEventListener('input', debounce(() => {
            checkSpelling(termInput, suggestionsList, card);
        }, 500));
        
        // Play audio
        if (autoAudio) {
            autoAudio.addEventListener('click', () => {
                const audioUrl = autoAudio.dataset.audioUrl;
                if (audioUrl) {
                    new Audio(audioUrl).play();
                }
            });
        }
        
        // Delete card
        if(deleteBtn) {
            deleteBtn.addEventListener('click', () => deleteCard(card));
        }

        // Image upload logic
        const imageUpload = card.querySelector('.image-upload');
        const fileInput = card.querySelector('input[type="file"]');
        const imgPreview = card.querySelector('.preview');
        const deleteImgBtn = card.querySelector('.delete-img-btn'); // Assuming a class for the delete button

        if (imageUpload && fileInput && imgPreview && deleteImgBtn) {
            imageUpload.addEventListener('click', (e) => {
                // Prevent event propagation if the click is on the delete button
                if (e.target !== deleteImgBtn && !deleteImgBtn.contains(e.target)) {
                    fileInput.click();
                }
            });

            fileInput.addEventListener('change', () => {
                const file = fileInput.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        imgPreview.src = e.target.result;
                        imgPreview.style.display = 'block';
                        deleteImgBtn.style.display = 'flex';
                    };
                    reader.readAsDataURL(file);
                }
            });

            deleteImgBtn.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent triggering the file input
                fileInput.value = ''; // Clear the file input
                imgPreview.src = '';
                imgPreview.style.display = 'none';
                deleteImgBtn.style.display = 'none';
            });
        }
    }
    
    // Initialize the first card
    document.querySelectorAll('.flashcard-section').forEach(initializeCard);
    
    // Event listener for adding a new card
    if(addCardBtn) {
        addCardBtn.addEventListener('click', createNewCard);
    }

    // Event listeners for Quick Add functionality
    if(generateCardsBtn) {
        generateCardsBtn.addEventListener('click', () => {
            const input = quickAddInput.value.trim();
            if (!input) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Empty Input',
                    text: 'Please enter some words separated by | (pipe) character.',
                    background: 'var(--background-light)',
                    color: 'var(--text-main)',
                    confirmButtonColor: 'var(--primary-color)'
                });
                return;
            }

            const words = parseQuickAddInput(input);

            // Check if there are existing cards with content
            const existingCards = flashcardContainer.querySelectorAll('.flashcard-section');
            const hasContent = Array.from(existingCards).some(card => {
                const termInput = card.querySelector('.term-input');
                return termInput && termInput.value.trim().length > 0;
            });

            if (hasContent && existingCards.length > 0) {
                // Show confirmation dialog
                Swal.fire({
                    icon: 'warning',
                    title: 'Replace Existing Cards?',
                    html: `This will <strong>clear all current cards</strong> and replace them with ${words.length} new cards.<br><br>Are you sure you want to continue?`,
                    showCancelButton: true,
                    confirmButtonText: 'Yes, Replace All',
                    cancelButtonText: 'Cancel',
                    confirmButtonColor: 'var(--primary-color)',
                    cancelButtonColor: '#6c757d',
                    background: 'var(--background-light)',
                    color: 'var(--text-main)'
                }).then((result) => {
                    if (result.isConfirmed) {
                        generateCardsFromWords(words);
                    }
                });
            } else {
                // No existing content, proceed directly
                generateCardsFromWords(words);
            }
        });
    }

    // Allow Enter key to trigger generation (with Ctrl/Cmd)
    if(quickAddInput) {
        quickAddInput.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                generateCardsBtn.click();
            }
        });
    }
    
    // Drag and drop sorting
    new Sortable(flashcardContainer, {
        animation: 150,
        handle: '.drag-handle',
        onEnd: updateCardNumbers
    });
    
    // Save all cards
    if(saveAllBtn) {
        saveAllBtn.addEventListener('click', () => {
            const formData = new FormData();
            const selectedDeckId = deckSelector.value;

            if (!selectedDeckId || selectedDeckId === 'new_deck') {
                 Swal.fire({
                    icon: 'warning',
                    title: 'Chưa chọn bộ thẻ',
                    text: 'Vui lòng chọn hoặc tạo một bộ thẻ trước khi lưu.',
                    background: 'var(--background-light)',
                    color: 'var(--text-main)',
                    confirmButtonColor: 'var(--primary-color)'
                });
                return;
            }

            // Check for duplicate warnings before saving
            const duplicateInputs = document.querySelectorAll('.term-input.duplicate-warning');
            if (duplicateInputs.length > 0) {
                const duplicateWords = Array.from(duplicateInputs).map(input => input.value.trim()).filter(word => word);
                Swal.fire({
                    icon: 'warning',
                    title: 'Duplicate Words Detected',
                    html: `The following words already exist in your vocabulary:<br><strong>${duplicateWords.join(', ')}</strong><br><br>Please remove or modify these words before saving.`,
                    background: 'var(--background-light)',
                    color: 'var(--text-main)',
                    confirmButtonColor: 'var(--primary-color)'
                });
                return;
            }

            formData.append('deck_id', selectedDeckId);

            const cards = document.querySelectorAll('.flashcard-section');
            let hasData = false;
            
            cards.forEach((card, idx) => {
                const word = card.querySelector('.term-input').value.trim();
                if (!word) return;

                hasData = true;

                formData.append(`flashcards-${idx}-word`, word);
                formData.append(`flashcards-${idx}-phonetic`, card.querySelector('.phonetic-input').value.trim());
                formData.append(`flashcards-${idx}-part_of_speech`, card.querySelector('.auto-pos').textContent.trim());
                formData.append(`flashcards-${idx}-english_definition`, card.querySelector('.definition-textarea').value.trim());
                formData.append(`flashcards-${idx}-vietnamese_definition`, card.querySelector('.vietnamese-textarea').value.trim());
                formData.append(`flashcards-${idx}-audio_url`, card.querySelector('.auto-audio').dataset.audioUrl || '');
                
                // Handle image file if present
                const imageInput = card.querySelector('input[type="file"]');
                if (imageInput.files[0]) {
                    formData.append(`flashcards-${idx}-image`, imageInput.files[0]);
                }
            });

            if (!hasData) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Chưa có dữ liệu',
                    text: 'Vui lòng nhập ít nhất một từ vựng.',
                    background: 'var(--background-light)',
                    color: 'var(--text-main)',
                    confirmButtonColor: 'var(--primary-color)'
                });
                return;
            }

            fetch("{% url 'save_flashcards' %}", {
                method: "POST",
                headers: { "X-CSRFToken": getCSRFToken() },
                body: formData,
            })
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: '{{ manual_texts.saved_successfully }}',
                        text: `{{ manual_texts.words_added_to_collection }}`.replace('{words}', data.saved.join(', ')),
                        background: 'var(--background-light)',
                        color: 'var(--text-main)',
                        confirmButtonColor: 'var(--primary-color)'
                    });
                    
                    // Reset the form by removing all but the first card and clearing it
                    const allCards = document.querySelectorAll('.flashcard-section');
                    allCards.forEach((card, index) => {
                        if (index > 0) {
                            card.remove();
                        }
                    });
                    const firstCard = document.querySelector('.flashcard-section');
                    if(firstCard){
                        firstCard.querySelectorAll('input, textarea').forEach(input => input.value = '');
                        firstCard.querySelector('.term-input').classList.remove('correct-spelling', 'incorrect-spelling', 'duplicate-warning');
                        resetCardUI(firstCard);
                        const imgPreview = firstCard.querySelector('.preview');
                        const deleteImgBtn = firstCard.querySelector('.delete-img-btn');
                        imgPreview.src = '';
                        imgPreview.style.display = 'none';
                        deleteImgBtn.style.display = 'none';
                        firstCard.querySelector('input[type="file"]').value = '';
                        updateCardNumbers();
                    }
                    window.scrollTo(0, 0); // Scroll to top
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Lỗi!',
                        text: `Đã có lỗi xảy ra: ${data.error || "Unknown error"}`,
                        background: 'var(--background-light)',
                        color: 'var(--text-main)',
                        confirmButtonColor: 'var(--primary-color)'
                    });
                }
            })
            .catch(err => {
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi kết nối',
                    text: `Không thể gửi yêu cầu đến máy chủ: ${err}`,
                    background: 'var(--background-light)',
                    color: 'var(--text-main)',
                    confirmButtonColor: 'var(--primary-color)'
                });
            });
        });
    }
});
</script>
{% endblock %}
 
 