#!/usr/bin/env python
"""
Test script to verify the enhanced audio UX improvements
"""
import os
import sys
import django


# Add the parent directory to Python path so we can import Django modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'learn_english_project.settings')
django.setup()

from django.contrib.auth import get_user_model
from vocabulary.models import Flashcard, Deck

User = get_user_model()

def create_test_data():
    """Create test data for UX testing"""
    print("🔧 Setting up test data for UX improvements...")
    
    # Get or create test user
    user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={'is_active': True}
    )
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"✅ Created test user: {user.email}")
    else:
        print(f"✅ Using existing test user: {user.email}")
    
    # Get or create test deck
    deck, created = Deck.objects.get_or_create(
        name='UX Test Deck',
        user=user
    )
    if created:
        print(f"✅ Created test deck: {deck.name}")
    else:
        print(f"✅ Using existing test deck: {deck.name}")
    
    # Create test flashcards with different audio states
    test_words = [
        {
            'word': 'example',
            'phonetic': '/ɪɡˈzæm.pəl/',
            'part_of_speech': 'noun',
            'audio_url': 'https://old-audio-example.mp3'
        },
        {
            'word': 'pronunciation',
            'phonetic': '/prəˌnʌn.siˈeɪ.ʃən/',
            'part_of_speech': 'noun',
            'audio_url': ''  # No audio initially
        },
        {
            'word': 'improvement',
            'phonetic': '/ɪmˈpruːv.mənt/',
            'part_of_speech': 'noun',
            'audio_url': 'https://old-audio-improvement.mp3'
        }
    ]
    
    created_cards = []
    for word_data in test_words:
        flashcard, created = Flashcard.objects.get_or_create(
            word=word_data['word'],
            user=user,
            deck=deck,
            defaults=word_data
        )
        created_cards.append(flashcard)
        
        if created:
            print(f"✅ Created flashcard: {flashcard.word} (ID: {flashcard.id})")
        else:
            print(f"✅ Using existing flashcard: {flashcard.word} (ID: {flashcard.id})")
    
    return user, deck, created_cards

def print_test_instructions():
    """Print instructions for manual UX testing"""
    print("\n🧪 UX IMPROVEMENTS TESTING GUIDE")
    print("=" * 60)
    
    print("\n1️⃣ CLICK-TO-SELECT FUNCTIONALITY TEST")
    print("-" * 40)
    print("✅ Start the development server: python manage.py runserver")
    print("✅ Navigate to the UX Test Deck detail page")
    print("✅ Click the enhanced audio button (🔍) next to any flashcard")
    print("✅ In the modal, try clicking anywhere on an audio option container")
    print("✅ Verify that clicking the container selects the radio button")
    print("✅ Verify that the container gets highlighted when selected")
    print("✅ Verify that clicking the preview button doesn't trigger selection")
    print("✅ Test on mobile/tablet to ensure touch-friendly behavior")
    
    print("\n2️⃣ SUCCESS NOTIFICATION TEST")
    print("-" * 40)
    print("✅ Select an audio option by clicking on its container")
    print("✅ Click 'Confirm Selection' button")
    print("✅ Verify that a success notification appears in the top-right corner")
    print("✅ Verify the notification says 'Audio pronunciation updated successfully!'")
    print("✅ Verify the notification has a green background (success style)")
    print("✅ Verify the notification auto-disappears after 3 seconds")
    print("✅ Verify the modal closes after successful update")
    print("✅ Verify the flashcard's audio button is updated on the page")
    
    print("\n3️⃣ ERROR HANDLING TEST")
    print("-" * 40)
    print("✅ Try clicking 'Confirm Selection' without selecting an option")
    print("✅ Verify that an error notification appears")
    print("✅ Verify the error notification has a red background")
    print("✅ Test with network disconnected to verify network error handling")
    
    print("\n4️⃣ VISUAL FEEDBACK TEST")
    print("-" * 40)
    print("✅ Hover over audio option containers")
    print("✅ Verify containers have hover effects (slight lift and color change)")
    print("✅ Verify selected containers have blue border and glow effect")
    print("✅ Verify error containers are grayed out and not clickable")
    print("✅ Verify the cursor changes to pointer over clickable containers")
    
    print("\n5️⃣ ACCESSIBILITY TEST")
    print("-" * 40)
    print("✅ Test keyboard navigation (Tab to navigate, Space/Enter to select)")
    print("✅ Test with screen reader if available")
    print("✅ Verify text selection is disabled on containers (user-select: none)")
    print("✅ Verify focus indicators are visible")
    
    print("\n📱 MOBILE/RESPONSIVE TEST")
    print("-" * 40)
    print("✅ Test on mobile device or use browser dev tools mobile view")
    print("✅ Verify containers are large enough for touch interaction")
    print("✅ Verify notifications are properly positioned on mobile")
    print("✅ Verify modal adapts to small screens")
    
    print("\n🎯 EXPECTED BEHAVIOR SUMMARY")
    print("-" * 40)
    print("• Clicking anywhere on audio option container selects it")
    print("• Selected containers have visual feedback (blue border + glow)")
    print("• Hover effects provide immediate visual feedback")
    print("• Success notifications appear after successful audio updates")
    print("• Error notifications appear for validation and network errors")
    print("• All notifications match existing app notification styling")
    print("• Notifications auto-disappear after 3 seconds")
    print("• Interface is touch-friendly on mobile devices")

def print_test_urls(cards):
    """Print URLs for testing"""
    print("\n🔗 TEST URLS")
    print("-" * 20)
    print("📄 Test HTML Page: http://localhost:8000/static/test/enhanced-audio-test.html")
    print("📄 API Debug Page: http://localhost:8000/static/test/api-debug.html")
    
    if cards:
        deck_id = cards[0].deck.id
        print(f"📄 UX Test Deck: http://localhost:8000/deck/{deck_id}/")
    
    print("\n💡 TIP: Open browser dev tools to see console logs and network requests")

if __name__ == '__main__':
    print("🚀 Enhanced Audio UX Improvements Test Setup")
    print("=" * 60)
    
    try:
        user, deck, cards = create_test_data()
        print_test_instructions()
        print_test_urls(cards)
        
        print(f"\n🎉 Test setup completed successfully!")
        print(f"📊 Created/verified {len(cards)} test flashcards")
        print(f"👤 Test user: {user.email}")
        print(f"📚 Test deck: {deck.name} (ID: {deck.id})")
        
        print("\n🚀 Ready for UX testing! Start the development server and follow the test guide above.")
        
    except Exception as e:
        print(f"❌ Error setting up test data: {e}")
        import traceback
        traceback.print_exc()
