{% extends "account/base.html" %}
{% load i18n %}

{% block title %}{% trans "Confirm Email" %}{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="max-w-md w-full space-y-8">
        <div class="bg-white rounded-lg shadow-xl p-8">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                    <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                </div>
                <h2 class="text-2xl font-bold text-gray-900 mb-2">{% trans "Confirm Your Email" %}</h2>
                <p class="text-gray-600 mb-6">{% trans "Click the button below to confirm your email address" %}</p>
            </div>

            {% if confirmation %}
                <form method="post" action="{% url 'account_confirm_email' confirmation.key %}">
                    {% csrf_token %}
                    <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-200">
                        {% trans "Confirm Email Address" %}
                    </button>
                </form>
            {% else %}
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                    <div class="text-red-700 text-sm text-center">
                        {% trans "Invalid confirmation link." %}
                    </div>
                </div>
            {% endif %}

            <div class="mt-6 text-center">
                <a href="{% url 'account_login' %}" class="text-indigo-600 hover:text-indigo-500 text-sm">
                    {% trans "Back to Login" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %} 