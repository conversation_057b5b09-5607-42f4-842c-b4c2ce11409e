{% extends "account/base.html" %}
{% load socialaccount %}

{% block title %}{{ manual_texts.signup_title }}{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="max-w-md w-full space-y-8">
        <div class="bg-white rounded-lg shadow-xl p-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">{{ manual_texts.create_account }}</h2>
                <p class="text-gray-600">{{ manual_texts.signup_subtitle }}</p>
            </div>

            <div class="mt-8 space-y-6">
                <!-- Google Signup Button -->
                <div class="text-center">
                    <a href="{% provider_login_url 'google' %}"
                       class="w-full flex justify-center items-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-200">
                        <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        {{ manual_texts.signup_with_google }}
                    </a>
                </div>

                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">{{ manual_texts.or }}</span>
                    </div>
                </div>

                <!-- Email Signup Form -->
                <form method="post" action="{% url 'account_signup' %}" class="space-y-6">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="bg-red-50 border border-red-200 rounded-md p-4">
                            <div class="text-red-700 text-sm">
                                {{ form.non_field_errors }}
                            </div>
                        </div>
                    {% endif %}

                    <div>
                        <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            {{ manual_texts.email_address }}
                        </label>
                        <div class="mt-1">
                            {{ form.email }}
                        </div>
                        {% if form.email.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.email.errors }}</div>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            {{ manual_texts.password }}
                        </label>
                        <div class="mt-1">
                            {{ form.password1 }}
                        </div>
                        {% if form.password1.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.password1.errors }}</div>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            {{ manual_texts.confirm_password }}
                        </label>
                        <div class="mt-1">
                            {{ form.password2 }}
                        </div>
                        {% if form.password2.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.password2.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-blue-700">
                                    {{ manual_texts.email_verification_notice }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div>
                        <button type="submit" class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200">
                            {{ manual_texts.create_account }}
                        </button>
                    </div>

                    <div class="text-center">
                        <span class="text-gray-600">{{ manual_texts.already_have_account }}</span>
                        <a href="{% url 'account_login' %}" class="font-medium text-indigo-600 hover:text-indigo-500 ml-1">
                            {{ manual_texts.sign_in }}
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    input[type="email"], input[type="password"] {
        appearance: none;
        border-radius: 0.375rem;
        border: 1px solid #d1d5db;
        padding: 0.75rem;
        width: 100%;
        font-size: 0.875rem;
        line-height: 1.25rem;
        color: #111827;
        background-color: #ffffff;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }
    
    input[type="email"]:focus, input[type="password"]:focus {
        outline: none;
        border-color: #6366f1;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }
</style>
{% endblock %} 