{% extends "base.html" %}
{% load static %}
{% block title %}📊 Enhanced Statistics - {{ manual_texts.learn_english }}{% endblock %}

{% block extra_css %}
<style>
.enhanced-stats-page {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  overflow-x: hidden;
  min-height: 100vh;
  box-sizing: border-box;
}

/* Prevent any potential infinite scroll issues */
html, body {
  overflow-x: hidden;
}

* {
  box-sizing: border-box;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 15px;
}

.stats-title {
  font-size: 2.5em;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-color), #8e44ad);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

.period-selector select {
  padding: 8px 12px;
  border: 1px solid var(--card-border-color);
  border-radius: 6px;
  background: var(--card-background);
  color: var(--card-text-main);
  font-size: 14px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.summary-card {
  background: var(--card-background);
  border: 1px solid var(--card-border-color);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.card-icon {
  font-size: 2em;
  margin-bottom: 10px;
}

.card-value {
  font-size: 1.8em;
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 5px;
}

.card-label {
  color: var(--card-text-muted);
  font-size: 0.9em;
}

.charts-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 40px;
}

.chart-container {
  background: var(--card-background);
  border: 1px solid var(--card-border-color);
  border-radius: 12px;
  padding: 20px;
  position: relative;
  height: 350px;
  overflow: hidden;
  box-sizing: border-box;
}

.chart-container h3 {
  margin: 0 0 20px 0;
  color: var(--card-text-main);
  font-size: 1.2em;
}

.chart-wrapper {
  position: relative;
  height: 280px;
  width: 100%;
  overflow: hidden;
}

.chart-wrapper canvas {
  max-width: 100%;
  max-height: 100%;
}

.single-chart-container {
  background: var(--card-background);
  border: 1px solid var(--card-border-color);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 40px;
  height: 400px;
}

.single-chart-wrapper {
  position: relative;
  height: 320px;
  width: 100%;
}

.recent-sessions {
  background: var(--card-background);
  border: 1px solid var(--card-border-color);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

.session-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--card-border-color);
}

.session-item:last-child {
  border-bottom: none;
}

.session-info {
  flex: 1;
}

.session-stats {
  text-align: right;
  min-width: 100px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stats-header {
    flex-direction: column;
    text-align: center;
  }

  .stats-title {
    font-size: 2em;
  }

  .charts-section {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .chart-container {
    height: 300px;
  }

  .chart-wrapper {
    height: 230px;
  }

  .summary-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .enhanced-stats-page {
    padding: 15px;
  }

  .summary-grid {
    grid-template-columns: 1fr 1fr;
  }

  .chart-container {
    height: 250px;
    padding: 15px;
  }

  .chart-wrapper {
    height: 180px;
  }
}
</style>
{% endblock %}

{% block content %}
<div class="enhanced-stats-page">
  <div class="stats-header">
    <h1 class="stats-title">📊 Learning Analytics</h1>

    <!-- Period Selector -->
    <div class="period-selector">
      <select id="periodSelect">
        {% for option in period_options %}
          <option value="{{ option.value }}" {% if option.value == current_period %}selected{% endif %}>
            {{ option.label }}
          </option>
        {% endfor %}
      </select>
    </div>
  </div>

  <!-- Summary Cards -->
  <div class="summary-grid">
    <div class="summary-card">
      <div class="card-icon">⏱️</div>
      <div class="card-value">{{ stats_summary.total_study_time_hours }}h</div>
      <div class="card-label">Total Study Time</div>
    </div>

    <div class="summary-card">
      <div class="card-icon">❓</div>
      <div class="card-value">{{ stats_summary.total_questions_answered }}</div>
      <div class="card-label">Questions Answered</div>
    </div>

    <div class="summary-card">
      <div class="card-icon">🎯</div>
      <div class="card-value">{{ stats_summary.accuracy_percentage }}%</div>
      <div class="card-label">Accuracy Rate</div>
    </div>

    <div class="summary-card">
      <div class="card-icon">🔥</div>
      <div class="card-value">{{ stats_summary.current_streak }}</div>
      <div class="card-label">Day Streak</div>
    </div>

    <div class="summary-card">
      <div class="card-icon">📚</div>
      <div class="card-value">{{ stats_summary.unique_words_studied }}</div>
      <div class="card-label">Words Studied</div>
    </div>

    <div class="summary-card">
      <div class="card-icon">📈</div>
      <div class="card-value">{{ stats_summary.consistency_percentage }}%</div>
      <div class="card-label">Consistency</div>
    </div>
  </div>

  <!-- Charts Section -->
  <div class="charts-section">
    <!-- Study Time Chart -->
    <div class="chart-container">
      <h3>📊 Daily Study Time (minutes)</h3>
      <div class="chart-wrapper">
        <canvas id="studyTimeChart"></canvas>
      </div>
    </div>

    <!-- Questions Chart -->
    <div class="chart-container">
      <h3>❓ Questions Answered</h3>
      <div class="chart-wrapper">
        <canvas id="questionsChart"></canvas>
      </div>
    </div>
  </div>

  <!-- Accuracy and Consistency Charts -->
  <div class="charts-section">
    <!-- Accuracy Chart -->
    <div class="chart-container">
      <h3>🎯 Daily Accuracy Rate</h3>
      <div class="chart-wrapper">
        <canvas id="accuracyChart"></canvas>
      </div>
    </div>

    <!-- Weekly Consistency Chart -->
    <div class="chart-container">
      <h3>📅 Weekly Consistency</h3>
      <div class="chart-wrapper">
        <canvas id="consistencyChart"></canvas>
      </div>
    </div>
  </div>

  <!-- Deck Distribution Chart -->
  {% if deck_labels %}
  <div class="single-chart-container">
    <h3>📚 Cards per Deck</h3>
    <div class="single-chart-wrapper">
      <canvas id="deckChart"></canvas>
    </div>
  </div>
  {% endif %}

  <!-- Recent Sessions -->
  {% if recent_sessions %}
  <div class="recent-sessions">
    <h3 style="margin-bottom:20px;color:var(--card-text-main);">🕒 Recent Study Sessions</h3>
    <div class="sessions-list">
      {% for session in recent_sessions %}
      <div class="session-item">
        <div class="session-info">
          <div style="font-weight:600;color:var(--card-text-main);">
            {{ session.get_study_mode_display }} - {{ session.session_start|date:"M d, H:i" }}
          </div>
          <div style="font-size:0.9em;color:var(--card-text-muted);">
            {{ session.total_questions }} questions • {{ session.accuracy_percentage }}% accuracy
          </div>
        </div>
        <div class="session-stats">
          <div style="font-weight:600;color:var(--primary-color);">{{ session.duration_formatted }}</div>
          <div style="font-size:0.9em;color:var(--card-text-muted);">{{ session.words_studied }} words</div>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
  {% endif %}
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Period selector change handler
  document.getElementById('periodSelect').addEventListener('change', function() {
    const period = this.value;
    window.location.href = `?period=${period}`;
  });

  // Chart configuration
  const chartConfig = {
    responsive: true,
    maintainAspectRatio: true,
    aspectRatio: 2,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(255,255,255,0.1)'
        },
        ticks: {
          color: 'var(--card-text-muted)',
          font: {
            size: 11
          }
        }
      },
      x: {
        grid: {
          color: 'rgba(255,255,255,0.1)'
        },
        ticks: {
          color: 'var(--card-text-muted)',
          font: {
            size: 11
          },
          maxRotation: 45
        }
      }
    },
    elements: {
      point: {
        radius: 3,
        hoverRadius: 5
      }
    }
  };

  // Study Time Chart
  const studyTimeCtx = document.getElementById('studyTimeChart').getContext('2d');
  new Chart(studyTimeCtx, {
    type: 'line',
    data: {
      labels: {{ chart_dates|safe }},
      datasets: [{
        data: {{ study_times|safe }},
        borderColor: '#6a6cff',
        backgroundColor: 'rgba(106,108,255,0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#6a6cff',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2
      }]
    },
    options: chartConfig
  });

  // Questions Chart
  const questionsCtx = document.getElementById('questionsChart').getContext('2d');
  new Chart(questionsCtx, {
    type: 'bar',
    data: {
      labels: {{ chart_dates|safe }},
      datasets: [{
        data: {{ questions_answered|safe }},
        backgroundColor: 'rgba(46,204,113,0.7)',
        borderColor: '#2ecc71',
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false
      }]
    },
    options: chartConfig
  });

  // Accuracy Chart
  const accuracyCtx = document.getElementById('accuracyChart').getContext('2d');
  new Chart(accuracyCtx, {
    type: 'line',
    data: {
      labels: {{ chart_dates|safe }},
      datasets: [{
        data: {{ accuracy_rates|safe }},
        borderColor: '#f39c12',
        backgroundColor: 'rgba(243,156,18,0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#f39c12',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2
      }]
    },
    options: {
      ...chartConfig,
      scales: {
        ...chartConfig.scales,
        y: {
          ...chartConfig.scales.y,
          max: 100,
          ticks: {
            ...chartConfig.scales.y.ticks,
            callback: function(value) {
              return value + '%';
            }
          }
        }
      }
    }
  });

  // Weekly Consistency Chart
  const consistencyCtx = document.getElementById('consistencyChart').getContext('2d');
  new Chart(consistencyCtx, {
    type: 'bar',
    data: {
      labels: {{ weekly_labels|safe }},
      datasets: [{
        data: {{ weekly_consistency|safe }},
        backgroundColor: 'rgba(155,89,182,0.7)',
        borderColor: '#9b59b6',
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false
      }]
    },
    options: {
      ...chartConfig,
      scales: {
        ...chartConfig.scales,
        y: {
          ...chartConfig.scales.y,
          max: 100,
          ticks: {
            ...chartConfig.scales.y.ticks,
            callback: function(value) {
              return value + '%';
            }
          }
        }
      }
    }
  });

  // Deck Chart
  {% if deck_labels %}
  const deckCtx = document.getElementById('deckChart').getContext('2d');
  new Chart(deckCtx, {
    type: 'bar',
    data: {
      labels: {{ deck_labels|safe }},
      datasets: [{
        data: {{ deck_counts|safe }},
        backgroundColor: 'rgba(106,108,255,0.7)',
        borderColor: '#6a6cff',
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false
      }]
    },
    options: {
      ...chartConfig,
      aspectRatio: 2.5,
      scales: {
        ...chartConfig.scales,
        x: {
          ...chartConfig.scales.x,
          ticks: {
            ...chartConfig.scales.x.ticks,
            maxRotation: 45,
            minRotation: 0
          }
        }
      }
    }
  });
  {% endif %}
});
</script>
{% endblock %}
