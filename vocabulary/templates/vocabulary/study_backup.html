{% extends "base.html" %} {% load static %} {% block title %}{{
manual_texts.study }} - {{ manual_texts.learn_english }}{% endblock %} {% block
extra_css %}
<link rel="stylesheet" href="{% static 'css/study.css' %}" />
{% endblock %} {% block content %}
<div class="study-page">
  <div class="study-header">
    <h1 class="study-title">📖 {{ manual_texts.study }}</h1>
    <p class="study-subtitle">{{ manual_texts.platform_description }}</p>
  </div>

  <!-- Study Mode Selection -->
  <div class="study-mode-section">
    <h2 class="section-title">{{ manual_texts.choose_study_mode }}</h2>

    <!-- Mode Slider Container -->
    <div class="mode-slider-container">
      <!-- Navigation Arrows -->
      <button
        class="slider-nav slider-prev"
        aria-label="Previous study mode"
        id="sliderPrev"
      >
        <i class="fas fa-chevron-left"></i>
      </button>

      <button
        class="slider-nav slider-next"
        aria-label="Next study mode"
        id="sliderNext"
      >
        <i class="fas fa-chevron-right"></i>
      </button>

      <!-- Mode Slider -->
      <div class="mode-slider" id="modeSlider">
        <div class="mode-slides">
          <!-- Deck Study Mode -->
          <div
            class="mode-slide active"
            data-mode="decks"
            role="tabpanel"
            aria-label="Deck study mode"
          >
            <label class="mode-option">
              <input
                type="radio"
                name="study_mode"
                value="decks"
                checked
                aria-describedby="deck-description"
              />
              <div class="mode-card">
                <div class="mode-icon" aria-hidden="true">📚</div>
                <div class="mode-content">
                  <h3>{{ manual_texts.deck_study }}</h3>
                  <p id="deck-description">
                    {{ manual_texts.deck_study_description }}
                  </p>
                </div>
              </div>
            </label>
          </div>

          <!-- Random Study Mode -->
          <div
            class="mode-slide"
            data-mode="random"
            role="tabpanel"
            aria-label="Random study mode"
          >
            <label class="mode-option">
              <input
                type="radio"
                name="study_mode"
                value="random"
                aria-describedby="random-description"
              />
              <div class="mode-card">
                <div class="mode-icon" aria-hidden="true">🎲</div>
                <div class="mode-content">
                  <h3>{{ manual_texts.random_study }}</h3>
                  <p id="random-description">
                    {{ manual_texts.random_study_description }}
                  </p>
                </div>
              </div>
            </label>
          </div>

          <!-- Review Mode -->
          <div
            class="mode-slide"
            data-mode="review"
            role="tabpanel"
            aria-label="Review incorrect words mode"
          >
            <label class="mode-option" id="reviewModeOption">
              <input
                type="radio"
                name="study_mode"
                value="review"
                aria-describedby="review-description"
              />
              <div class="mode-card">
                <div class="mode-icon" aria-hidden="true">🔄</div>
                <div class="mode-content">
                  <h3>{{ manual_texts.review_incorrect_words }}</h3>
                  <p id="review-description">
                    {{ manual_texts.review_study_description }}
                  </p>
                  <div
                    class="review-count"
                    id="reviewCount"
                    style="display: none"
                  >
                    <span id="reviewCountText"
                      >0 {{ manual_texts.incorrect_words_count }}</span
                    >
                  </div>
                </div>
              </div>
            </label>
          </div>

          <!-- Favorites Mode -->
          <div
            class="mode-slide"
            data-mode="favorites"
            role="tabpanel"
            aria-label="Study favorite words mode"
          >
            <label class="mode-option" id="favoritesModeOption">
              <input
                type="radio"
                name="study_mode"
                value="favorites"
                aria-describedby="favorites-description"
              />
              <div class="mode-card">
                <div class="mode-icon" aria-hidden="true">❤️</div>
                <div class="mode-content">
                  <h3>{{ manual_texts.study_favorites }}</h3>
                  <p id="favorites-description">
                    {{ manual_texts.favorites_study_description }}
                  </p>
                  <div
                    class="favorites-count"
                    id="favoritesCount"
                    style="display: none"
                  >
                    <span id="favoritesCountText"
                      >0 {{ manual_texts.favorite_words_count }}</span
                    >
                  </div>
                </div>
              </div>
            </label>
          </div>
        </div>
      </div>

      <!-- Slider Indicators -->
      <div class="slider-indicators">
        <button
          class="indicator active"
          data-slide="0"
          aria-label="Deck study mode"
        ></button>
        <button
          class="indicator"
          data-slide="1"
          aria-label="Random study mode"
        ></button>
        <button
          class="indicator"
          data-slide="2"
          aria-label="Review mode"
        ></button>
        <button
          class="indicator"
          data-slide="3"
          aria-label="Favorites mode"
        ></button>
      </div>
    </div>
  </div>

  <!-- Deck Study Options -->
  <div id="deckStudyOptions" class="study-options-section">
    <h3 class="options-title">{{ manual_texts.select_decks }}</h3>

    <div class="deck-options">
      <div class="deck-selection">
        <div class="deck-grid">
          {% for deck in decks %}
          <label class="deck-option">
            <input
              type="checkbox"
              name="deck_ids"
              value="{{ deck.id }}"
              {%
              if
              forloop.first
              %}checked{%
              endif
              %}
            />
            <div class="deck-card">
              <div class="deck-info">
                <h4 class="deck-name">{{ deck.name }}</h4>
                <p class="deck-description">{{ deck.description }}</p>
                <div class="deck-stats">
                  <span class="card-count"
                    >{{ deck.flashcard_count }} {{ manual_texts.flashcards
                    }}</span
                  >
                </div>
              </div>
            </div>
          </label>
          {% empty %}
          <div class="no-decks">
            <p>{{ manual_texts.no_decks_available }}</p>
            <a href="{% url 'deck_list' %}" class="create-deck-link"
              >{{ manual_texts.create_first_deck }}</a
            >
          </div>
          {% endfor %}
        </div>
      </div>
    </div>

    <button id="startBtn" class="start-button" disabled>
      <span class="button-icon">🚀</span>
      {{ manual_texts.start_studying }}
    </button>
  </div>

  <!-- Random Study Options -->
  <div id="randomStudyOptions" class="study-options-section hidden">
    <h3 class="options-title">{{ manual_texts.random_study_settings }}</h3>

    <div class="random-options">
      <div class="word-count-selection">
        <label for="randomWordCount" class="option-label"
          >{{ manual_texts.number_of_words }}</label
        >
        <select id="randomWordCount" class="word-count-select">
          <option value="10">10 {{ manual_texts.words }}</option>
          <option value="20" selected>20 {{ manual_texts.words }}</option>
          <option value="30">30 {{ manual_texts.words }}</option>
          <option value="50">50 {{ manual_texts.words }}</option>
          <option value="100">100 {{ manual_texts.words }}</option>
        </select>
      </div>

      <div class="random-description">
        <p>{{ manual_texts.random_mode_description }}</p>
      </div>
    </div>

    <button id="startRandomBtn" class="start-button">
      <span class="button-icon">🎲</span>
      {{ manual_texts.start_random_study }}
    </button>
  </div>

  <!-- Review Study Options -->
  <div id="reviewStudyOptions" class="study-options-section hidden">
    <h3 class="options-title">{{ manual_texts.review_incorrect_words }}</h3>

    <div class="review-options">
      <div class="review-info">
        <div class="review-description">
          <p>{{ manual_texts.review_mode_description }}</p>
          <div class="review-details" id="reviewDetails" style="display: none">
            <div class="review-breakdown">
              <div class="breakdown-item">
                <span class="breakdown-label"
                  >{{ manual_texts.total_incorrect }}:</span
                >
                <span class="breakdown-count" id="totalIncorrectCount">0</span>
              </div>
              <div class="breakdown-item">
                <span class="breakdown-label"
                  >{{ manual_texts.unresolved }}:</span
                >
                <span class="breakdown-count" id="unresolvedCount">0</span>
              </div>
              <div class="breakdown-item">
                <span class="breakdown-label"
                  >{{ manual_texts.resolved }}:</span
                >
                <span class="breakdown-count" id="resolvedCount">0</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <button id="startReviewBtn" class="start-button" disabled>
      <span class="button-icon">🔄</span>
      {{ manual_texts.start_review }}
    </button>
  </div>

  <!-- Favorites Study Options -->
  <div id="favoritesStudyOptions" class="study-options-section hidden">
    <h3 class="options-title">{{ manual_texts.study_favorites }}</h3>

    <div class="favorites-options">
      <div class="favorites-info">
        <div class="favorites-description">
          <p>{{ manual_texts.favorites_mode_description }}</p>
          <div
            class="favorites-details"
            id="favoritesDetails"
            style="display: none"
          >
            <div class="favorites-breakdown">
              <div class="breakdown-item">
                <span class="breakdown-label"
                  >{{ manual_texts.total_favorites }}:</span
                >
                <span class="breakdown-count" id="totalFavoritesCount">0</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <button id="startFavoritesBtn" class="start-button" disabled>
      <span class="button-icon">❤️</span>
      {{ manual_texts.start_favorites_study }}
    </button>
  </div>

  <div id="studyArea" class="study-area">
    <div class="study-header-bar">
      <button id="backBtn" class="back-button">
        <i class="fas fa-arrow-left"></i>
        {{ manual_texts.back_to_selection }}
      </button>
      <div class="header-controls">
        <div id="statsInfo" class="stats-display">
          {{ manual_texts.correct }}: 0 | {{ manual_texts.incorrect }}: 0
        </div>
        <div class="audio-settings">
          <label class="audio-toggle">
            <input type="checkbox" id="audioToggle" checked />
            <span class="toggle-slider"></span>
            <span class="toggle-label">
              <i class="fas fa-volume-up"></i>
              {{ manual_texts.sound_feedback }}
            </span>
          </label>
        </div>
      </div>
    </div>

    <div id="cardBox" class="flashcard-container">
      <div class="card-header">
        <div class="word-section">
          <h2 id="cardWord" class="card-word"></h2>
          <button id="audioButton" class="audio-button" style="display: none">
            <i class="fas fa-volume-up"></i>
          </button>
        </div>
        <p id="cardPhonetic" class="card-phonetic"></p>
      </div>

      <img id="cardImage" src="" alt="image" class="card-image" />

      <div id="answerSection" class="answer-section">
        <div id="cardDefs" class="card-definitions"></div>
        <div id="optionsArea" class="options-area"></div>
        <div id="feedbackMsg" class="feedback-message"></div>
      </div>

      <div id="gradeButtons" class="grade-buttons">
        <button class="grade-btn grade-again" data-grade="0">
          <span class="grade-icon">😰</span>
          {{ manual_texts.grade_again }}
        </button>
        <button class="grade-btn grade-hard" data-grade="1">
          <span class="grade-icon">😅</span>
          {{ manual_texts.grade_hard }}
        </button>
        <button class="grade-btn grade-good" data-grade="2">
          <span class="grade-icon">😊</span>
          {{ manual_texts.grade_good }}
        </button>
        <button class="grade-btn grade-easy" data-grade="3">
          <span class="grade-icon">😎</span>
          {{ manual_texts.grade_easy }}
        </button>
      </div>

      <p id="noCardMsg" class="no-cards-message">
        {{ manual_texts.no_cards_due }}
      </p>
    </div>
  </div>
</div>

<!-- Review Completion Success Modal -->
<div id="reviewCompletionModal" class="modal-overlay" style="display: none">
  <div class="modal-content success-modal">
    <div class="modal-header">
      <h2 class="modal-title">{{ manual_texts.review_completed_title }}</h2>
    </div>
    <div class="modal-body">
      <div class="success-icon">🎉</div>
      <p class="success-message">{{ manual_texts.review_completed_message }}</p>
    </div>
    <div class="modal-footer">
      <button id="continueStudyingBtn" class="modal-button primary">
        {{ manual_texts.continue_studying }}
      </button>
    </div>
  </div>
</div>

{% endblock %} {% block extra_js %}
<script>
  const STUDY_CFG = {
    nextUrl: "{% url 'api_next_question' %}",
    submitUrl: "{% url 'api_submit_answer' %}",
    csrfToken: document.querySelector('meta[name="csrf-token"]').content,
    labels: {
      correct: "{{ manual_texts.correct }}",
      incorrect: "{{ manual_texts.incorrect }}",
      check: "{{ manual_texts.check }}",
      placeholder: "{{ manual_texts.answer_placeholder }}",
      grade_again: "{{ manual_texts.grade_again }}",
      grade_hard: "{{ manual_texts.grade_hard }}",
      grade_good: "{{ manual_texts.grade_good }}",
      grade_easy: "{{ manual_texts.grade_easy }}",
      no_cards_due: "{{ manual_texts.no_cards_due }}",
      play_audio: "{{ manual_texts.play_audio }}",
      listen_and_type: "{{ manual_texts.listen_and_type }}",
      type_what_you_hear: "{{ manual_texts.type_what_you_hear }}",
      correct_answer: "{{ manual_texts.correct_answer }}",
      incorrect_answer: "{{ manual_texts.incorrect_answer }}",
      replay_audio: "{{ manual_texts.replay_audio }}",
      english_label: "{{ manual_texts.english_label }}",
      vietnamese_label: "{{ manual_texts.vietnamese_label }}",
      review_completed_title: "{{ manual_texts.review_completed_title }}",
      review_completed_message: "{{ manual_texts.review_completed_message }}",
      continue_studying: "{{ manual_texts.continue_studying }}",
    },
  };
</script>
<script src="{% static 'js/dictionary-utils.js' %}"></script>
<script src="{% static 'js/study.js' %}"></script>
{% endblock %}
