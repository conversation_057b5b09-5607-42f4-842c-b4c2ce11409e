{% extends "account/base.html" %}
{% load i18n %}

{% block title %}{% trans "Password Reset Sent" %}{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="max-w-md w-full space-y-8">
        <div class="bg-white rounded-lg shadow-xl p-8">
            <div class="text-center">
                <!-- Mail Sent Icon -->
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                    <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-gray-900 mb-2">
                    {% trans "Check Your Email" %}
                </h2>
                
                <p class="text-gray-600 mb-6">
                    {% trans "We have sent you an email with instructions to reset your password. Please check your inbox and follow the link provided." %}
                </p>
            </div>

            <!-- Success Info -->
            <div class="mt-8 bg-green-50 border border-green-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-green-800">
                            {% trans "Email Sent Successfully" %}
                        </h3>
                        <div class="mt-2 text-sm text-green-700">
                            <ul class="list-disc pl-5 space-y-1">
                                <li>{% trans "Check your inbox for the reset email" %}</li>
                                <li>{% trans "Don't forget to check your spam folder" %}</li>
                                <li>{% trans "The link will expire in 24 hours" %}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-8 flex flex-col space-y-4">
                <a href="{% url 'account_login' %}" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200">
                    {% trans "Back to Login" %}
                </a>
                
                <a href="{% url 'account_reset_password' %}" class="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200">
                    {% trans "Resend Email" %}
                </a>
            </div>

            <!-- Contact Support -->
            <div class="mt-6 text-center">
                <p class="text-xs text-gray-500">
                    {% trans "Having trouble? Contact us for support." %}
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %} 