# Generated by Django 5.2.1 on 2025-07-26 14:48

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('vocabulary', '0011_incorrectwordreview'),
    ]

    operations = [
        migrations.AlterField(
            model_name='flashcard',
            name='difficulty_score',
            field=models.FloatField(blank=True, default=None, help_text='Difficulty level: 0.0=Again, 0.33=Hard, 0.67=Good, 1.0=Easy', null=True),
        ),
        migrations.AlterField(
            model_name='flashcard',
            name='ease_factor',
            field=models.FloatField(default=2.5, help_text='Legacy SM-2 ease factor (not used)'),
        ),
        migrations.AlterField(
            model_name='flashcard',
            name='interval',
            field=models.PositiveIntegerField(default=0, help_text='Legacy interval days (not used)'),
        ),
        migrations.AlterField(
            model_name='flashcard',
            name='last_reviewed',
            field=models.DateTimeField(blank=True, help_text='Last time card was reviewed', null=True),
        ),
        migrations.AlterField(
            model_name='flashcard',
            name='next_review',
            field=models.DateField(default=django.utils.timezone.now, help_text='Legacy next review date (not used)'),
        ),
        migrations.AlterField(
            model_name='flashcard',
            name='repetitions',
            field=models.PositiveIntegerField(default=0, help_text='Legacy successful reviews count (not used)'),
        ),
    ]
