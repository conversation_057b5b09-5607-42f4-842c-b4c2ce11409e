/* Modern Study Page Styles */
:root {
  --primary-color-dark: #4f46e5;
  --success-color: #10b981;
  --error-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #3b82f6;
  --gradient-primary: linear-gradient(135deg, var(--primary-color), #8b5cf6);
  --gradient-success: linear-gradient(135deg, #10b981, #059669);
  --gradient-error: linear-gradient(135deg, #ef4444, #dc2626);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Study Page Layout */
.study-page {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
}

.study-header {
  text-align: center;
  margin-bottom: 40px;
}

.study-title {
  font-size: 2.5rem;
  font-weight: 800;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 8px;
}

.study-subtitle {
  color: var(--card-text-muted);
  font-size: 1.1rem;
  margin: 0;
}

/* Study Mode Selection */
.study-mode-section {
  background: var(--card-background);
  border: 1px solid var(--card-border-color);
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: var(--shadow-md);
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--card-text-main);
  margin-bottom: 20px;
  text-align: center;
}

/* Mode Slider Container */
.mode-slider-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 20px 0;
}

.mode-slider-wrapper {
  flex: 1;
  overflow: hidden;
  border-radius: 16px;
  position: relative;
}

.mode-slider {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
  touch-action: pan-x;
  user-select: none;
}

.mode-slide {
  min-width: 100%;
  flex-shrink: 0;
  padding: 0 10px;
  box-sizing: border-box;
}

.mode-slide.active .mode-card {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.15);
}

/* Smooth transitions for all interactive elements */
.mode-slide {
  transition: opacity 0.4s ease;
}

.mode-slide:not(.active) {
  opacity: 0.7;
}

.mode-slide.active {
  opacity: 1;
}

.mode-option {
  cursor: pointer;
  position: relative;
  display: block;
  width: 100%;
}

.mode-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

/* Slider Navigation Arrows */
.slider-nav {
  background: var(--card-background);
  border: 2px solid var(--card-border-color);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.5rem;
  color: var(--text-color);
  z-index: 10;
}

.slider-nav:hover {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: white;
  transform: scale(1.1);
}

.slider-nav:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.slider-nav:disabled:hover {
  border-color: var(--card-border-color);
  background: var(--card-background);
  color: var(--text-color);
}

.nav-arrow {
  font-weight: bold;
  line-height: 1;
}

.mode-card {
  background: var(--card-background);
  border: 2px solid var(--card-border-color);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  height: 120px;
  box-sizing: border-box;
}

/* Slider Indicators */
.slider-indicators {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: var(--card-border-color);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: var(--primary-color);
  transform: scale(1.2);
}

.indicator:hover {
  background: var(--primary-color);
  opacity: 0.7;
}

.mode-option input[type="radio"]:checked + .mode-card {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, var(--primary-color)10, var(--card-background));
  box-shadow: var(--shadow-lg);
  transform: scale(1.05);
}

.mode-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gradient-primary);
  border-radius: 12px;
  color: white;
}

.mode-content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--card-text-main);
  margin: 0 0 5px 0;
}

.mode-content p {
  font-size: 0.9rem;
  color: var(--card-text-muted);
  margin: 0;
}

/* Study Options Sections */
.study-options-section {
  background: var(--card-background);
  border: 1px solid var(--card-border-color);
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: var(--shadow-md);
}

.study-options-section.hidden {
  display: none;
}

.options-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--card-text-main);
  margin-bottom: 20px;
  text-align: center;
}

/* Deck Selector */
.deck-selector {
  position: relative;
  margin-bottom: 25px;
}

.dropdown-toggle {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: var(--card-background);
  border: 2px solid var(--card-border-color);
  border-radius: 12px;
  color: var(--card-text-main);
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dropdown-toggle:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.dropdown-arrow {
  color: var(--card-text-muted);
  transition: transform 0.3s ease;
}

.dropdown-toggle.active .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--card-background);
  border: 1px solid var(--card-border-color);
  border-radius: 12px;
  box-shadow: var(--shadow-xl);
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  margin-top: 5px;
  display: none;
}

.dropdown-menu.show {
  display: block;
}

.deck-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid var(--card-border-color);
}

.deck-option:last-child {
  border-bottom: none;
}

.deck-option:hover {
  background: rgba(var(--primary-color-rgb), 0.05);
}

.deck-option input[type="checkbox"] {
  margin-right: 12px;
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
}

.deck-name {
  flex: 1;
  color: var(--card-text-main);
  font-weight: 500;
}

.deck-count {
  color: var(--card-text-muted);
  font-size: 0.9rem;
  background: rgba(var(--primary-color-rgb), 0.1);
  padding: 2px 8px;
  border-radius: 12px;
}

.no-decks-message {
  padding: 20px;
  text-align: center;
  color: var(--card-text-muted);
  font-style: italic;
}

/* Random Study Options */
.random-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 25px;
}

.word-count-input {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.word-count-input label {
  font-weight: 600;
  color: var(--card-text-main);
  font-size: 0.95rem;
}

.word-count-input input {
  padding: 12px 16px;
  border: 2px solid var(--card-border-color);
  border-radius: 8px;
  background: var(--card-background);
  color: var(--card-text-main);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.word-count-input input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.available-words-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(var(--primary-color-rgb), 0.05);
  border-radius: 8px;
  border: 1px solid rgba(var(--primary-color-rgb), 0.1);
}

.info-label {
  color: var(--card-text-main);
  font-weight: 500;
}

.info-value {
  color: var(--primary-color);
  font-weight: 700;
  font-size: 1.1rem;
}

/* Start Button */
.start-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: 100%;
  padding: 16px 24px;
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
}

.start-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.start-button:active {
  transform: translateY(0);
}

.start-button:disabled,
.start-button.disabled {
  background: #e0e0e0;
  color: #999;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.6;
}

.start-button:disabled:hover,
.start-button.disabled:hover {
  background: #e0e0e0;
  transform: none;
  box-shadow: none;
}

/* Review Completion Success Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.success-modal {
  text-align: center;
}

.modal-header {
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid #e0e0e0;
}

.modal-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
}

.modal-body {
  padding: 2rem;
}

.success-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.success-message {
  font-size: 1.1rem;
  color: var(--text-color);
  line-height: 1.5;
  margin: 0 0 1.5rem 0;
}

.modal-footer {
  padding: 1rem 2rem 2rem 2rem;
}

.modal-button {
  background: linear-gradient(135deg, var(--primary-color) 0%, #667eea 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.modal-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.modal-button:active {
  transform: translateY(0);
}

.button-icon {
  font-size: 1.2rem;
}

/* Study Area */
.study-area {
  display: none;
  animation: fadeIn 0.5s ease-in-out;
}

.study-area.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.study-header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 0 10px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: var(--primary-color);
  color: white;
  transform: translateX(-2px);
}

/* Timer Display */
.timer-display {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: var(--shadow-sm);
  min-width: 80px;
}

.timer-display i {
  font-size: 1rem;
  opacity: 0.9;
}

#timerText {
  font-family: 'Courier New', monospace;
  font-size: 1rem;
  font-weight: 700;
  letter-spacing: 0.5px;
  transition: color 0.3s ease, opacity 0.3s ease;
}

/* Timer Paused State */
.timer-display.paused {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  animation: pulse-paused 2s infinite;
}

.timer-display.paused i {
  opacity: 0.7;
}

@keyframes pulse-paused {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Additional visual indicator for paused state */
.timer-display.paused::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #f59e0b, #d97706);
  border-radius: 10px;
  z-index: -1;
  opacity: 0.3;
}

.stats-display {
  background: var(--card-background);
  border: 1px solid var(--card-border-color);
  padding: 10px 16px;
  border-radius: 8px;
  color: var(--card-text-main);
  font-weight: 600;
  box-shadow: var(--shadow-sm);
}

/* Audio Settings Toggle */
.audio-settings {
  display: flex;
  align-items: center;
}

.audio-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  user-select: none;
}

.toggle-slider {
  position: relative;
  width: 50px;
  height: 24px;
  background: #ccc;
  border-radius: 24px;
  transition: background 0.3s ease;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

#audioToggle {
  display: none;
}

#audioToggle:checked + .toggle-slider {
  background: var(--primary-color);
}

#audioToggle:checked + .toggle-slider::before {
  transform: translateX(26px);
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--card-text-main);
  font-size: 0.9rem;
  font-weight: 500;
}

.toggle-label i {
  font-size: 1rem;
}

/* Review Mode Styling */
.review-count {
  margin-top: 8px;
  padding: 4px 8px;
  background: rgba(var(--primary-color-rgb), 0.1);
  border: 1px solid var(--primary-color);
  border-radius: 12px;
  font-size: 0.8rem;
  color: var(--primary-color);
  font-weight: 600;
  text-align: center;
}

/* Review Study Options */
.review-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.review-info {
  background: rgba(var(--primary-color-rgb), 0.05);
  border: 1px solid rgba(var(--primary-color-rgb), 0.1);
  border-radius: 8px;
  padding: 1rem;
}

.review-description {
  color: var(--text-color);
  line-height: 1.5;
}

.review-description p {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
}

.review-details {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(var(--primary-color-rgb), 0.1);
}

.review-breakdown {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: rgba(var(--primary-color-rgb), 0.05);
  border-radius: 6px;
  font-size: 0.85rem;
}

.breakdown-label {
  color: var(--text-color);
  font-weight: 500;
}

.breakdown-count {
  color: var(--primary-color);
  font-weight: 600;
  background: rgba(var(--primary-color-rgb), 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  min-width: 20px;
  text-align: center;
}

#reviewModeOption .mode-card {
  position: relative;
}

#reviewModeOption.disabled .mode-card {
  opacity: 0.5;
  cursor: not-allowed;
}

#reviewModeOption.disabled input[type="radio"] {
  cursor: not-allowed;
}

/* Flashcard Container - Optimized for no scrolling */
.flashcard-container {
  background: var(--card-background);
  border: 1px solid var(--card-border-color);
  border-radius: 16px;
  padding: 25px;
  min-height: 350px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  text-align: center;
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

/* Special layout for dictation mode to prevent grade button overflow */
.flashcard-container:has(.dictation-mode) {
  justify-content: flex-start;
  gap: 15px;
}

/* Fallback for browsers that don't support :has() */
.flashcard-container.dictation-layout {
  justify-content: flex-start;
  gap: 15px;
}

.flashcard-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.card-header {
  width: 100%;
  margin-bottom: 20px;
}

.word-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 10px;
}

.card-word {
  font-size: 2.2rem;
  font-weight: 800;
  color: var(--primary-color);
  margin: 0 0 10px 0;
  line-height: 1.1;
}

/* Clickable word link styling */
.word-link {
  color: #4f46e5;
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 4px 8px;
  display: inline-block;
  font-weight: inherit;
}

.word-link:hover {
  color: #3730a3;
  background: rgba(79, 70, 229, 0.1);
  text-decoration: underline;
  transform: translateY(-1px);
}

.word-link:active {
  transform: translateY(0);
}

.definition-item {
  font-size: 1.2rem;
  font-weight: 500;
  color: var(--card-text-main);
  margin: 10px 0;
  padding: 8px 0;
  line-height: 1.4;
}

.definition-item strong {
  color: var(--primary-color);
  font-weight: 600;
}

.audio-button {
  background: var(--gradient-success);
  color: white;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.audio-button:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.audio-button:active {
  transform: scale(0.95);
}

/* Favorite button styling */
.favorite-button {
  background: transparent;
  border: 2px solid var(--border-color);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  position: relative;
  overflow: hidden;
}

.favorite-button:hover {
  transform: scale(1.1);
  border-color: var(--primary-color);
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.favorite-button:active {
  transform: scale(0.95);
}

.favorite-button.favorited {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.favorite-button.favorited:hover {
  border-color: #dc2626;
  background: rgba(239, 68, 68, 0.2);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.favorite-icon {
  transition: all 0.3s ease;
}

.favorite-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.favorite-button:disabled:hover {
  transform: none;
  border-color: var(--border-color);
  box-shadow: none;
}

.card-phonetic {
  color: var(--card-text-muted);
  font-style: italic;
  font-size: 1rem;
  margin: 0 0 10px 0;
}

/* Card Image */
.card-image {
  max-width: 300px;
  max-height: 200px;
  border-radius: 12px;
  margin: 20px 0;
  box-shadow: var(--shadow-md);
  display: none;
}

.card-image.show {
  display: block;
}

/* Answer Section */
.answer-section {
  width: 100%;
  display: none;
}

.answer-section.active {
  display: block;
}

.card-definitions {
  color: #ffffff;
  white-space: pre-line;
  margin-bottom: 15px;
  font-size: 1.1rem;
  line-height: 1.5;
  display: none;
  text-align: center;
  padding: 15px 20px;
  background: rgba(79, 70, 229, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(79, 70, 229, 0.2);
  font-weight: 500;
}

.card-definitions.show {
  display: block;
}

.options-area {
  margin: 15px 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  align-items: center;
  justify-items: center;
  max-width: 450px;
  margin-left: auto;
  margin-right: auto;
}

/* Input mode specific layout - override grid for proper centering */
.options-area:has(.input-row), .options-area.input-mode {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15px;
  min-height: 120px;
}

/* Dictation mode specific layout - optimized for grade button visibility */
.options-area.dictation-mode {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15px;
  min-height: 160px;
  max-height: 180px;
}

.dictation-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15px;
  width: 100%;
  max-width: 400px;
}

/* Input row for both dictation and regular input modes */
.dictation-input-row, .input-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* Specific input row styling for regular input mode */
.input-row {
  padding: 10px 0;
  align-items: stretch; /* Ensure both elements have same height */
}

.dictation-input-row {
  /* Specific styles for dictation mode if needed */
}

.dictation-mode .type-input {
  min-width: 250px;
  max-width: 350px;
  text-align: center;
}

.dictation-mode .check-btn {
  min-width: 100px;
}

/* Input mode specific styling for consistent sizing and alignment */
.input-row .type-input {
  min-width: 250px;
  max-width: 350px;
  flex: 1;
  text-align: center;
  margin: 0;
  align-self: center;
}

.input-row .check-btn {
  min-width: 100px;
  flex-shrink: 0;
  margin: 0;
  align-self: center;
  height: fit-content;
}

/* Replay Audio Button for Dictation Mode */
.replay-audio-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.replay-audio-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.replay-audio-btn:active {
  transform: translateY(0);
}

.replay-audio-btn i {
  font-size: 1rem;
}

.option-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  min-width: 180px;
  max-width: 220px;
  box-shadow: var(--shadow-sm);
  text-align: center;
}

.option-btn:hover {
  background: var(--primary-color-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.option-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.type-input {
  padding: 12px 16px;
  border: 2px solid var(--card-border-color);
  border-radius: 8px;
  background: var(--card-background);
  color: var(--card-text-main);
  font-size: 1rem;
  width: 100%;
  max-width: 300px;
  transition: all 0.3s ease;
}

/* Override width for input elements inside input rows */
.input-row .type-input, .dictation-input-row .type-input {
  width: auto;
  flex: 1;
}

.type-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.check-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
}

/* Remove margin-top for check buttons inside input rows */
.input-row .check-btn, .dictation-input-row .check-btn {
  margin-top: 0;
}

.check-btn:hover {
  background: var(--primary-color-dark);
  transform: translateY(-1px);
}

.feedback-message {
  font-weight: 700;
  font-size: 1.2rem;
  margin: 20px 0;
  padding: 12px 20px;
  border-radius: 8px;
  display: none;
}

.feedback-message.success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.feedback-message.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.feedback-message.show {
  display: block;
}



/* Grade Buttons - Compact layout */
.grade-buttons {
  display: none;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-top: 15px;
  width: 100%;
  max-width: 350px;
}

/* Grade buttons in dictation mode - extra compact */
.dictation-layout .grade-buttons,
.flashcard-container:has(.dictation-mode) .grade-buttons {
  margin-top: 10px;
  gap: 6px;
  max-width: 320px;
}

.grade-buttons.show {
  display: grid !important;
}

/* Ensure grade buttons are never accidentally hidden */
#gradeButtons.grade-buttons.show {
  display: grid !important;
  visibility: visible !important;
}

.grade-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 10px 6px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  box-shadow: var(--shadow-sm);
  font-size: 0.85rem;
}

.grade-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.grade-icon {
  font-size: 1.5rem;
}

.grade-again {
  background: var(--gradient-error);
}

.grade-hard {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.grade-good {
  background: var(--gradient-success);
}

.grade-easy {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

/* No Cards Message */
.no-cards-message {
  display: none;
  color: var(--card-text-muted);
  font-size: 1.2rem;
  font-style: italic;
  text-align: center;
  margin-top: 40px;
}

.no-cards-message.show {
  display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
  .study-page {
    padding: 15px;
  }

  .study-title {
    font-size: 2rem;
  }

  /* Mobile Slider Adjustments */
  .mode-slider-container {
    gap: 10px;
  }

  .slider-nav {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .mode-slide {
    padding: 0 5px;
  }

  .study-header-bar {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .header-controls {
    flex-direction: column;
    gap: 10px;
  }

  .timer-display {
    justify-content: center;
    min-width: 70px;
    padding: 8px 12px;
    font-size: 0.85rem;
  }

  .audio-toggle {
    justify-content: center;
  }

  .toggle-label {
    font-size: 0.8rem;
  }

  .flashcard-container {
    padding: 25px;
    min-height: 350px;
  }

  .card-word {
    font-size: 2rem;
  }

  .word-section {
    flex-direction: column;
    gap: 10px;
  }

  .grade-buttons {
    grid-template-columns: 1fr 1fr;
    gap: 10px;
  }

  .options-area {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .options-area.dictation-mode {
    gap: 12px;
    min-height: 120px;
    max-height: 140px;
  }

  .dictation-input-row, .input-row {
    flex-direction: column;
    gap: 12px;
    max-width: 100%;
    padding: 15px 10px;
  }

  .input-row .type-input {
    min-width: 200px;
    max-width: 280px;
    width: auto;
    flex: 1;
    margin: 0;
  }

  .input-row .check-btn {
    min-width: 120px;
    width: auto;
    margin: 0;
    flex-shrink: 0;
  }

  .dictation-mode .type-input {
    min-width: 200px;
    max-width: 280px;
  }

  .replay-audio-btn {
    padding: 10px 16px;
    font-size: 0.9rem;
  }

  .option-btn {
    min-width: 150px;
    padding: 8px 14px;
    max-width: none;
    font-size: 0.9rem;
  }

  /* Mobile optimizations for no scrolling */
  .flashcard-container {
    padding: 15px;
    min-height: 300px;
    max-height: 85vh;
  }

  .card-word {
    font-size: 1.8rem;
    margin-bottom: 8px;
  }

  .card-definitions {
    font-size: 1rem;
    padding: 12px 15px;
    margin-bottom: 12px;
  }

  .grade-buttons {
    gap: 6px;
    margin-top: 12px;
    max-width: 320px;
  }

  /* Extra compact grade buttons for dictation mode on mobile */
  .dictation-layout .grade-buttons,
  .flashcard-container:has(.dictation-mode) .grade-buttons {
    margin-top: 8px;
    gap: 4px;
    max-width: 300px;
  }

  .grade-btn {
    padding: 8px 4px;
    font-size: 0.8rem;
    gap: 3px;
  }

  /* Smaller grade buttons in dictation mode on mobile */
  .dictation-layout .grade-btn,
  .flashcard-container:has(.dictation-mode) .grade-btn {
    padding: 6px 3px;
    font-size: 0.75rem;
    gap: 2px;
  }
}

@media (max-width: 480px) {
  .study-page {
    padding: 10px;
  }

  .study-title {
    font-size: 1.8rem;
  }

  .flashcard-container {
    padding: 20px;
    min-height: 300px;
  }

  .card-word {
    font-size: 1.8rem;
  }

  .grade-buttons {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .mode-card {
    padding: 15px;
  }

  .mode-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
}

/* Deck select */
.deck-select {
  width: 100%;
  max-height: 180px;
  padding: 10px;
  background: var(--card-background);
  color: var(--card-text-main);
  border: 1px solid var(--card-border-color);
  border-radius: 8px;
  overflow-y: auto;
  transition: border-color .15s ease, box-shadow .15s ease;
}
.deck-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(99,102,241,0.4);
}

/* Primary button (Start / Back) */
.primary-btn {
  background: var(--primary-color);
  color: #fff;
  border: none;
  padding: 10px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background .15s ease, transform .15s ease, box-shadow .15s ease;
}
.primary-btn:hover {
  background: var(--primary-color-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(0,0,0,0.2);
}

/* Next card button */
.next-card-btn {
  background: #4dc0b5 !important;
  color: #fff !important;
  padding: 10px 20px !important;
  border: none !important;
  border-radius: 6px !important;
  margin-top: 15px !important;
  cursor: pointer !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
  display: block !important;
  margin-left: auto !important;
  margin-right: auto !important;
}
.next-card-btn:hover {
  background: #3da89f !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 10px rgba(0,0,0,0.2) !important;
}

/* Hidden elements after answer submission - scoped to study area to avoid navigation conflicts */
.study-area .hidden,
.study-options-section .hidden,
.options-area .hidden,
.option-btn.hidden,
.input-row.hidden,
.dictation-input-row.hidden,
.check-btn.hidden,
.type-input.hidden {
  display: none !important;
}

/* Ensure Tailwind navigation classes work properly - override any conflicts */
nav .hidden.md\:block {
  display: none !important;
}

@media (min-width: 768px) {
  nav .hidden.md\:block {
    display: block !important;
  }
}

/* Specific rules for hidden input-row elements to override flex display */
.input-row.hidden,
.dictation-input-row.hidden {
  display: none !important;
  visibility: hidden !important;
}

.options-area.input-mode .input-row.hidden {
  display: none !important;
  visibility: hidden !important;
}

/* Hide child elements of hidden input rows */
.input-row.hidden .type-input,
.input-row.hidden .check-btn,
.dictation-input-row.hidden .type-input,
.dictation-input-row.hidden .check-btn {
  display: none !important;
  visibility: hidden !important;
}

/* Ultra-specific rules to override any conflicting styles */
.options-area .input-row.hidden,
.options-area.input-mode .input-row.hidden,
div.input-row.hidden {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Smooth transition for hiding elements */
.option-btn, .input-row, .dictation-input-row, .replay-audio-btn {
  transition: opacity 0.3s ease;
}

/* Ensure proper spacing when elements are hidden */
.options-area:has(.hidden), .options-area.input-mode:has(.hidden) {
  min-height: auto;
}

/* Additional centering support for input mode */
.options-area.input-mode {
  text-align: center;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
}

.options-area.input-mode .input-row:not(.hidden) {
  margin: 0 auto;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Ensure input elements are properly aligned */
.input-row > * {
  vertical-align: middle;
  box-sizing: border-box;
}

/* Final override to ensure perfect alignment */
.options-area.input-mode .input-row .type-input,
.options-area.input-mode .input-row .check-btn {
  margin: 0 !important;
  padding-top: 12px !important;
  padding-bottom: 12px !important;
  height: auto !important;
  line-height: normal !important;
  vertical-align: middle !important;
}

/* Ensure both elements have the same height (only when not hidden) */
.input-row:not(.hidden) .type-input,
.input-row:not(.hidden) .check-btn {
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Disabled button styles (kept for backward compatibility) */
.option-btn:disabled, .check-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}
.option-btn:disabled:hover, .check-btn:disabled:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* Extra small screens optimization for dictation mode */
@media (max-height: 600px) {
  .flashcard-container {
    max-height: 90vh;
    padding: 12px;
  }

  .options-area.dictation-mode {
    min-height: 100px;
    max-height: 120px;
    gap: 10px;
  }

  .dictation-container {
    gap: 10px;
  }

  .replay-audio-btn {
    padding: 8px 12px;
    font-size: 0.85rem;
  }

  .dictation-layout .grade-buttons,
  .flashcard-container:has(.dictation-mode) .grade-buttons {
    margin-top: 5px;
    gap: 3px;
    max-width: 280px;
  }

  .dictation-layout .grade-btn,
  .flashcard-container:has(.dictation-mode) .grade-btn {
    padding: 5px 2px;
    font-size: 0.7rem;
    gap: 1px;
  }
}

.flashcard-correct {
  border-color: #10b981 !important; /* green-500 */
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
  transition: border-color 0.3s, box-shadow 0.3s;
}
.flashcard-incorrect {
  border-color: #ef4444 !important; /* red-500 */
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.18);
  transition: border-color 0.3s, box-shadow 0.3s;
}