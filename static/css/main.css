/* ==========================================================================
   LearnEnglish App - Main Stylesheet
   ========================================================================== */

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: #18182f;
  color: #fff;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  min-height: 100vh;
}

/* Add this to hide scrollbar for webkit browsers */
.no-scrollbar::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

.pagination-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin: 0 4px;
    border-radius: 50%;
    background-color: #555; /* Gray color for inactive dots */
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.pagination-dot.active {
    background-color: #A78BFA; /* Primary color for active dot */
}

/* ==========================================================================
   Main Container & Layout
   ========================================================================== */
/* Note: The main layout is now handled by Tailwind CSS classes in base.html */
/* The styles below are for specific components that are not part of the main layout. */


/* ==========================================================================
   Dashboard Styles
   ========================================================================== */

.dashboard-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.welcome-section {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-section h1 {
  font-size: 2.5em;
  margin-bottom: 10px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-section p {
  color: #b0b0b0;
  font-size: 1.1em;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: #232345;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  border: 1px solid #3a3a5c;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.stat-icon {
  font-size: 2em;
  opacity: 0.8;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2em;
  font-weight: bold;
  color: #667eea;
  line-height: 1;
}

.stat-label {
  color: #b0b0b0;
  font-size: 0.9em;
  margin-top: 4px;
}

.action-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 28px;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  font-size: 1.1em;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.action-btn.secondary {
  background: #232345;
  color: #b0b0b0;
  border: 2px solid #3a3a5c;
}

.action-btn.secondary:hover {
  background: #3a3a5c;
  color: white;
  transform: translateY(-2px);
}

/* ==========================================================================
   Button Styles
   ========================================================================== */

.button, 
a.button {
  display: inline-block;
  padding: 12px 24px;
  background: #667eea;
  color: #fff;
  border-radius: 8px;
  text-decoration: none;
  margin: 8px 0;
  transition: all 0.3s ease;
  font-weight: 500;
  border: none;
  cursor: pointer;
  font-size: 1em;
}

.button:hover, 
a.button:hover {
  background: #764ba2;
  transform: translateY(-2px);
  color: #fff;
  text-decoration: none;
}

.button.secondary {
  background: #3a3a5c;
  color: #b0b0b0;
}

.button.secondary:hover {
  background: #4a4a6c;
  color: #fff;
}

/* ==========================================================================
   Form Styles
   ========================================================================== */

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  color: #b0b0b0;
  font-weight: 500;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 12px 16px;
  background: #232345;
  border: 1px solid #3a3a5c;
  border-radius: 8px;
  color: #fff;
  font-size: 1em;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* ==========================================================================
   Card Styles
   ========================================================================== */

.card {
  background: #232345;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #3a3a5c;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.card-header {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #3a3a5c;
}

.card-title {
  color: #fff;
  font-size: 1.2em;
  font-weight: 600;
  margin: 0;
}

/* ==========================================================================
   Deck Detail Page Styles (Carousel)
   ========================================================================== */

.deck-detail-page {
    max-width: 1200px;
    margin: 20px auto;
    padding: 0 20px;
}

.page-header {
    text-align: left;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-header h1 {
    font-size: 2.2em;
    font-weight: 700;
    color: var(--text-main);
}

.back-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
}

/* Word Item (Flashcard) Styles - re-using existing .word-item from original CSS */
.word-item {
    background-color: var(--card-background);
    border: 1px solid var(--card-border-color);
    padding: 20px;
    border-radius: 12px;
    transition: all 0.2s ease-in-out;
    box-shadow: var(--card-shadow);
    width: 100%; /* Ensure word-item takes full width of carousel-slide */
    height: 100%; /* Ensure word-item takes full height of carousel-slide */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start; /* Align content to start */
}

.word-item:hover {
    transform: translateY(-4px);
    border-color: var(--primary-color);
    box-shadow: var(--card-shadow-hover);
}

.word-item .word {
    font-size: 1.7em;
    font-weight: 600;
    color: var(--card-text-main);
    margin-bottom: 5px;
}

.word-item .phonetic {
    font-family: sans-serif;
    color: var(--card-text-muted);
    margin-bottom: 15px;
    font-style: italic;
    font-size: 1em;
    display: flex;
    align-items: center;
    gap: 10px;
}

.word-item .audio-icon {
    color: var(--text-muted);
    cursor: pointer;
    transition: color 0.2s ease-in-out;
    display: inline-block;
}

.word-item .audio-icon:hover {
    color: var(--primary-color);
}

.word-item .definition {
    font-size: 0.9em;
    line-height: 1.6;
    color: var(--card-text-muted);
    margin-bottom: 8px;
}

/* Carousel Specific Styles - For Centering and Peek */
.carousel-container {
    position: relative;
    width: 100%;
    max-width: 600px; /* Main card (500px) + 50px peek on each side = 600px */
    margin: 0 auto;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: var(--card-shadow-hover);
}

.carousel-slides {
    display: flex;
    transition: transform 0.5s ease-in-out;
    /* We will control spacing directly on .carousel-slide using margin-right */
}

.carousel-slide {
    flex: 0 0 auto;
    width: 500px; /* Desired width of the active card */
    box-sizing: border-box;
    display: flex;
    flex-direction: column; /* Ensure content stacks nicely */
    justify-content: center;
    align-items: flex-start; /* Align text to left within slide */
    padding: 20px;
    background-color: var(--card-background);
    border: 1px solid var(--card-border-color);
    border-radius: 12px;
    transition: all 0.5s ease-in-out;
    opacity: 0.5; /* Default for non-active slides */
    transform: scale(0.85); /* Smaller scale for peek effect */
    position: relative;
    z-index: 1;
    margin-right: 20px; /* Space between cards */
}

/* Last slide doesn't need margin-right */
.carousel-slide:last-child {
    margin-right: 0;
}

.carousel-slide.active {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    z-index: 2;
}

.carousel-slide.prev-slide,
.carousel-slide.next-slide {
    opacity: 0.7;
    transform: scale(0.9);
}

.carousel-nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    padding: 10px;
    cursor: pointer;
    z-index: 10;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5em;
    transition: background-color 0.3s ease;
}

.carousel-nav-btn:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

.carousel-prev {
    left: 10px;
}

.carousel-next {
    right: 10px;
}

.carousel-pagination {
    text-align: center;
    margin-top: 20px;
}

.pagination-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    background-color: var(--text-muted);
    border-radius: 50%;
    margin: 0 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.pagination-dot.active {
    background-color: var(--primary-color);
}

/* ==========================================================================
   Edit Flashcard Styles
   ========================================================================== */

.edit-card-btn {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.word-item-tailwind:hover .edit-card-btn {
    opacity: 1;
}

.card-edit-mode {
    max-height: 80vh;
    overflow-y: auto;
}

.card-edit-mode::-webkit-scrollbar {
    width: 6px;
}

.card-edit-mode::-webkit-scrollbar-track {
    background: #374151;
    border-radius: 3px;
}

.card-edit-mode::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 3px;
}

.card-edit-mode::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

.definition-pair {
    border-left: 3px solid #6366f1;
}

.edit-mode-header {
    position: sticky;
    top: 0;
    background: #111827;
    z-index: 10;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
    border-bottom: 1px solid #374151;
}

/* Success/Error message animations */
.message-toast {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading state for save button */
.save-card-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Edit Mode Navigation Hiding */
body[data-edit-mode="true"] .carousel-nav-btn,
body[data-edit-mode="true"] .carousel-pagination {
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

body[data-edit-mode="true"] .carousel-slides {
    scroll-snap-type: none;
    pointer-events: none;
}

body[data-edit-mode="true"] .carousel-slides .card-edit-mode {
    pointer-events: all;
}

/* Edit mode visual feedback */
.edit-mode-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.05);
    z-index: 5;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    border-radius: 12px;
}

body[data-edit-mode="true"] .edit-mode-overlay {
    opacity: 1;
}

/* Position overlay relative to carousel container */
.carousel-container {
    position: relative;
}

/* ESC hint */
.esc-hint {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 0.875rem;
    z-index: 60;
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
    transition: all 0.3s ease;
    pointer-events: none;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

body[data-edit-mode="true"] .esc-hint {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

.esc-hint kbd {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 0.75rem;
    margin: 0 4px;
}

/* Disable text selection during edit mode for better UX */
body[data-edit-mode="true"] .carousel-container {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

body[data-edit-mode="true"] .card-edit-mode {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
}

/* Responsive adjustments for edit mode */
@media (max-width: 640px) {
    .card-edit-mode {
        max-height: 90vh;
    }

    .edit-mode-header {
        position: relative;
    }

    .edit-mode-header .flex {
        flex-direction: column;
        gap: 0.5rem;
    }

    .edit-mode-header .flex > div {
        width: 100%;
    }

    .edit-mode-header button {
        width: 100%;
    }

    .esc-hint {
        top: 10px;
        font-size: 0.75rem;
        padding: 8px 16px;
        border-radius: 20px;
    }

    .esc-hint kbd {
        padding: 1px 4px;
        font-size: 0.7rem;
    }
}

/* ==========================================================================
   Simplified Audio Button Styling
   ========================================================================== */

/* Enhanced audio button styling */
.audio-icon-tailwind {
    transition: all 0.3s ease;
    border-radius: 50%;
    padding: 4px;
}

.audio-icon-tailwind:hover {
    background: rgba(99, 102, 241, 0.1);
    transform: scale(1.1);
}

/* ==========================================================================
   Deck Name Editing
   ========================================================================== */

#edit-deck-name-btn {
    opacity: 0;
    transition: opacity 0.3s ease;
}

#deck-name-view:hover #edit-deck-name-btn {
    opacity: 1;
}

#deck-name-input {
    min-width: 300px;
    max-width: 500px;
}

#deck-name-input:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Deck name edit buttons */
#save-deck-name-btn:disabled,
#cancel-deck-edit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Responsive deck name editing */
@media (max-width: 768px) {
    #deck-name-edit {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        width: 100%;
    }

    #deck-name-input {
        min-width: auto;
        max-width: none;
        width: 100%;
        font-size: 1.5rem;
    }

    #deck-name-edit .flex {
        flex-direction: row;
        gap: 8px;
    }

    #save-deck-name-btn,
    #cancel-deck-edit-btn {
        flex: 1;
    }
}

/* Filter controls */
.audio-filter-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding: 16px;
    background: rgba(55, 65, 81, 0.5);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(75, 85, 99, 0.3);
}

.audio-filter-controls label {
    color: #d1d5db;
    font-weight: 500;
    font-size: 0.875rem;
}

.audio-filter-controls select {
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 8px;
    color: white;
    padding: 8px 12px;
    font-size: 0.875rem;
    min-width: 180px;
}

.audio-filter-controls select:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.audio-stats {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 0.875rem;
    color: #9ca3af;
}

.audio-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.audio-stats .stat-item i {
    font-size: 1rem;
}

.audio-stats .stat-item.has-audio {
    color: #22c55e;
}

.audio-stats .stat-item.no-audio {
    color: #ef4444;
}

/* Audio fetching button */
#fetch-missing-audio-btn {
    position: relative;
    overflow: hidden;
}

#fetch-missing-audio-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

#fetch-missing-audio-btn.loading {
    color: transparent;
}

#fetch-missing-audio-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Audio fetch progress */
.audio-fetch-progress {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 16px 20px;
    border-radius: 8px;
    z-index: 1000;
    min-width: 300px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.audio-fetch-progress .progress-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-weight: 600;
}

.audio-fetch-progress .progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.audio-fetch-progress .progress-fill {
    height: 100%;
    background: #3b82f6;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.audio-fetch-progress .progress-text {
    font-size: 0.875rem;
    color: #d1d5db;
}

.audio-fetch-progress .close-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.audio-fetch-progress .close-btn:hover {
    color: white;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .audio-filter-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .audio-filter-controls select {
        min-width: auto;
    }

    .audio-stats {
        justify-content: center;
        flex-wrap: wrap;
        gap: 12px;
    }
}