# Generated by Django 5.2.1 on 2025-07-22 10:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('vocabulary', '0008_flashcard_ease_factor_flashcard_interval_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='flashcard',
            name='correct_reviews',
            field=models.PositiveIntegerField(default=0, help_text='Number of correct reviews'),
        ),
        migrations.AddField(
            model_name='flashcard',
            name='difficulty_score',
            field=models.FloatField(default=0.0, help_text='Difficulty score based on user performance'),
        ),
        migrations.AddField(
            model_name='flashcard',
            name='last_seen_date',
            field=models.DateField(blank=True, help_text='Last date this card was shown', null=True),
        ),
        migrations.AddField(
            model_name='flashcard',
            name='times_seen_today',
            field=models.PositiveIntegerField(default=0, help_text='Number of times seen today (reset daily)'),
        ),
        migrations.AddField(
            model_name='flashcard',
            name='total_reviews',
            field=models.PositiveIntegerField(default=0, help_text='Total number of times reviewed'),
        ),
    ]
