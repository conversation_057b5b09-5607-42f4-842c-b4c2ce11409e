# Generated by Django 4.2.23 on 2025-07-10 04:06

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('vocabulary', '0007_deck_flashcard_deck'),
    ]

    operations = [
        migrations.AddField(
            model_name='flashcard',
            name='ease_factor',
            field=models.FloatField(default=2.5, help_text='SM-2 ease factor'),
        ),
        migrations.AddField(
            model_name='flashcard',
            name='interval',
            field=models.PositiveIntegerField(default=0, help_text='Interval (days) until next review'),
        ),
        migrations.AddField(
            model_name='flashcard',
            name='last_reviewed',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='flashcard',
            name='next_review',
            field=models.DateField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='flashcard',
            name='repetitions',
            field=models.PositiveIntegerField(default=0, help_text='Number of successful reviews in a row'),
        ),
    ]
