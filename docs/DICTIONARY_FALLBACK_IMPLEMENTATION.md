# Dictionary Link System with Intelligent Fallback

## Overview

This document describes the implementation of an intelligent dictionary link system with automatic fallback between Cambridge Dictionary and Oxford Learner's Dictionary. The system ensures users always have access to word definitions with optimal response times.

## How It Works

### Primary Dictionary: Cambridge Dictionary

1. **Target URL**: `https://dictionary.cambridge.org/dictionary/english/{word}`
2. **Response time check**: 3-second timeout for accessibility verification
3. **Advantages**: Comprehensive definitions, audio pronunciation, example sentences

### Fallback Dictionary: Oxford Learner's Dictionary

1. **Fallback URL**: `https://www.oxfordlearnersdictionaries.com/definition/english/{word}`
2. **Automatic switch**: Triggered when Cambridge is slow (>3s) or unresponsive
3. **Advantages**: Learner-focused definitions, clear explanations, reliable availability

## Implementation Details

### Core Components

#### 1. DictionaryUtils Class (`static/js/dictionary-utils.js`)

- **Main utility class** that handles all dictionary link functionality
- **Key methods**:
  - `openDictionaryLink(word, options)`: Main method for opening dictionary links with fallback
  - `checkSiteAccessibility(url)`: Checks if a website is accessible
  - `createDictionaryLink(word, options)`: Creates new dictionary link elements
  - `enhanceExistingLink(linkElement, word, options)`: Enhances existing links with fallback

#### 2. Study Page Integration (`static/js/study.js`)

- **Location**: Lines 603-660 in `static/js/study.js`
- **Function**: `submitAnswer()` - When revealing correct answers after questions
- **Enhancement**: Replaces direct Cambridge Dictionary links with fallback-enabled links

#### 3. Deck Detail Page Integration

- **Template**: `vocabulary/templates/vocabulary/deck_detail.html` (line 131)
- **JavaScript**: `static/js/deck_detail.js` (lines 1171-1209)
- **Functions**:
  - `initializeDictionaryLinks()`: Enhances all dictionary links on page load
  - `updateCardDisplay()`: Enhances links when cards are updated

### Configuration Options

```javascript
DictionaryUtils.openDictionaryLink(word, {
  newTab: true, // Open in new tab (default: true)
  onFallback: (word, fallbackUrl) => {
    // Called when fallback is used
    console.log(`Using fallback for ${word}: ${fallbackUrl}`);
  },
  onError: (error) => {
    // Called when both attempts fail
    console.error(`Dictionary access failed: ${error}`);
  },
});
```

### Error Handling

The implementation includes comprehensive error handling:

1. **Network errors**: Caught and handled gracefully
2. **Timeout errors**: 5-second timeout for accessibility checks
3. **CORS issues**: Uses `no-cors` mode for accessibility checks
4. **Fallback failures**: Last resort attempts and error callbacks

## Usage Examples

### Creating New Dictionary Links

```javascript
const link = DictionaryUtils.createDictionaryLink("hello", {
  className: "word-link",
  text: "hello",
  onFallback: (word, url) => console.log(`Fallback: ${url}`),
  onError: (error) => console.error(`Error: ${error}`),
});
document.body.appendChild(link);
```

### Enhancing Existing Links

```javascript
const existingLink = document.querySelector('a[data-word="hello"]');
DictionaryUtils.enhanceExistingLink(existingLink, "hello", {
  onFallback: (word, url) => console.log(`Using fallback: ${url}`),
  onError: (error) => console.error(`Failed: ${error}`),
});
```

### Direct API Usage

```javascript
await DictionaryUtils.openDictionaryLink("programming", {
  newTab: true,
  onFallback: (word, fallbackUrl) => {
    console.log(`Fallback triggered for ${word}`);
  },
});
```

## Testing

### Test File

A comprehensive test file is available at `static/test/dictionary-fallback-test.html` that includes:

- Interactive word links for manual testing
- Programmatic API tests
- Console logging and status display
- Multiple test scenarios (normal words, complex words, special characters)

### Test Scenarios

1. **Normal operation**: Main site accessible, direct link works
2. **Fallback operation**: Main site unavailable, fallback URL used
3. **Complete failure**: Both attempts fail, error handling triggered
4. **Special characters**: URL encoding works correctly

## Browser Compatibility

The implementation uses modern JavaScript features:

- **ES6 Classes**: Supported in all modern browsers
- **Async/Await**: Supported in all modern browsers
- **Fetch API**: Supported in all modern browsers
- **Promises**: Supported in all modern browsers

### Fallback for Older Browsers

If `DictionaryUtils` is not available (e.g., in older browsers), the application falls back to the original direct Cambridge Dictionary links.

## Performance Considerations

1. **Lightweight checks**: Uses HTTP HEAD requests for accessibility checks
2. **Timeout limits**: 5-second timeout prevents long waits
3. **Caching**: Browser caches accessibility results naturally
4. **No-cors mode**: Avoids CORS preflight requests

## Security Considerations

1. **URL encoding**: All words are properly URL-encoded
2. **Target="\_blank"**: New tabs opened with `noopener,noreferrer`
3. **No-cors mode**: Prevents reading response content, only checks accessibility
4. **Input validation**: Word parameters are validated before processing

## Maintenance

### Monitoring

- Console logs provide detailed information about fallback usage
- Error callbacks allow for custom monitoring and analytics
- Test file enables manual verification of functionality

### Updates

- Cambridge Dictionary URLs are centralized in the `DictionaryUtils` class
- Easy to update URLs or add new dictionary sources
- Modular design allows for easy extension

## Integration Checklist

When integrating the fallback mechanism:

1. ✅ Include `dictionary-utils.js` before other scripts
2. ✅ Add `dictionary-word-link` class to dictionary links
3. ✅ Add `data-word` attribute with the word value
4. ✅ Call `initializeDictionaryLinks()` after page load
5. ✅ Enhance dynamically created links with `enhanceExistingLink()`
6. ✅ Test with the provided test file

## Future Enhancements

Potential improvements for future versions:

- Support for multiple dictionary sources
- Offline dictionary support
- User preference for dictionary source
- Analytics for fallback usage
- Retry mechanisms with exponential backoff
