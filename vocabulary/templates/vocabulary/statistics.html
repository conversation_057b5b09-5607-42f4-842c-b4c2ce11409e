{% extends "base.html" %}
{% load static %}
{% block title %}{{ manual_texts.statistics_title }} - {{ manual_texts.learn_english }}{% endblock %}

{% block content %}
<div class="stats-page" style="max-width:1200px;margin:0 auto;padding:20px;">
  <h1 style="font-size:2.2em;font-weight:700;margin-bottom:30px;background:linear-gradient(135deg,var(--primary-color),#8e44ad);-webkit-background-clip:text;-webkit-text-fill-color:transparent;">📊 {{ manual_texts.statistics_title }}</h1>

  {% if total_decks == 0 %}
    <p style="color:var(--text-muted);font-size:1.1em;">{{ manual_texts.no_decks_message }}</p>
  {% else %}
  <div class="summary-grid" style="display:grid;grid-template-columns:repeat(auto-fit,minmax(220px,1fr));gap:20px;margin-bottom:40px;">
    <div class="summary-card" style="background:var(--card-background);border:1px solid var(--card-border-color);border-radius:12px;padding:24px;text-align:center;">
      <div style="font-size:1.4em;color:var(--primary-color);font-weight:600;">{{ total_decks }}</div>
      <div style="color:var(--card-text-muted);">{{ manual_texts.total_decks }}</div>
    </div>
    <div class="summary-card" style="background:var(--card-background);border:1px solid var(--card-border-color);border-radius:12px;padding:24px;text-align:center;">
      <div style="font-size:1.4em;color:var(--primary-color);font-weight:600;">{{ total_cards }}</div>
      <div style="color:var(--card-text-muted);">{{ manual_texts.total_cards }}</div>
    </div>
    <div class="summary-card" style="background:var(--card-background);border:1px solid var(--card-border-color);border-radius:12px;padding:24px;text-align:center;">
      <div style="font-size:1.4em;color:var(--primary-color);font-weight:600;">{{ avg_cards }}</div>
      <div style="color:var(--card-text-muted);">{{ manual_texts.average_cards_per_deck }}</div>
    </div>
  </div>

  <h2 style="font-size:1.5em;font-weight:600;margin-bottom:15px;color:var(--card-text-main);">{{ manual_texts.cards_per_deck }}</h2>
  <canvas id="deckChart" height="120"></canvas>
  {% endif %}
</div>

{% endblock %}

{% block extra_js %}
{% if total_decks > 0 %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function(){
      const ctx = document.getElementById('deckChart').getContext('2d');
      const chart = new Chart(ctx, {
          type: 'bar',
          data: {
              labels: {{ deck_labels|safe }},
              datasets: [{
                  label: '{{ manual_texts.cards_per_deck }}',
                  data: {{ deck_counts|safe }},
                  backgroundColor: 'rgba(106,108,255,0.6)',
                  borderColor: 'rgba(106,108,255,1)',
                  borderWidth: 1,
              }]
          },
          options: {
              responsive: true,
              scales: {
                  y: { beginAtZero: true, ticks: { precision:0 } }
              }
          }
      });
  });
</script>
{% endif %}
{% endblock %} 