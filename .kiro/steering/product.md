# Product Overview

LearnEnglish is a Django-based vocabulary learning application that helps users build and study English vocabulary through flashcards with spaced repetition.

## Core Features

- **Flashcard Management**: Create, organize, and manage vocabulary flashcards with definitions, phonetics, audio, and images
- **Deck Organization**: Group flashcards into themed decks for better organization
- **Spaced Repetition**: SM-2 algorithm implementation for optimized learning schedules
- **Multi-language Support**: English/Vietnamese interface with i18n support
- **Study Modes**: Multiple choice and typing practice modes
- **User Authentication**: Email-based authentication with Google OAuth integration
- **Progress Tracking**: Statistics and progress monitoring for learning analytics

## Target Users

English language learners, particularly Vietnamese speakers, who want to systematically build their vocabulary through evidence-based spaced repetition techniques.

## Key Value Propositions

- Personalized learning through spaced repetition algorithms
- Rich multimedia flashcards with images and audio
- Organized deck-based learning structure
- Progress tracking and statistics
- Bilingual interface support