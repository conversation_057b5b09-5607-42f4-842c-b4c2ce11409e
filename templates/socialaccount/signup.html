{% extends "account/base.html" %}
{% load i18n %}
{% load allauth %}

{% block title %}{% trans "Signup" %}{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="max-w-md w-full space-y-8">
        <div class="bg-white rounded-lg shadow-xl p-8">
            <div class="text-center">
                <!-- User Plus Icon -->
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                    <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-gray-900 mb-2">
                    {% trans "Complete Sign Up" %}
                </h2>
                
                <p class="text-gray-600 mb-6">
                    {% blocktrans with provider_name=account.get_provider.name site_name=site.name %}You are about to use your {{provider_name}} account to login to {{site_name}}. As a final step, please complete the following form:{% endblocktrans %}
                </p>
            </div>

            <div class="mt-8">
                {% url 'socialaccount_signup' as action_url %}
                <form method="post" action="{{ action_url }}" class="space-y-6">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="bg-red-50 border border-red-200 rounded-md p-4">
                            <div class="text-red-700 text-sm">
                                {{ form.non_field_errors }}
                            </div>
                        </div>
                    {% endif %}

                    <!-- Render form fields -->
                    {% for field in form %}
                        <div>
                            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                {{ field.label }}
                            </label>
                            <div class="mt-1">
                                {{ field }}
                            </div>
                            {% if field.errors %}
                                <div class="text-red-600 text-sm mt-1">{{ field.errors }}</div>
                            {% endif %}
                        </div>
                    {% endfor %}

                    {{ redirect_field }}

                    <div class="flex flex-col space-y-4">
                        <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-200">
                            {% trans "Sign Up" %}
                        </button>

                        <div class="text-center">
                            <a href="{% url 'account_login' %}" class="text-indigo-600 hover:text-indigo-500 text-sm font-medium">
                                ← Back to Login
                            </a>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Provider Info -->
            <div class="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700">
                            Your {{ account.get_provider.name }} account will be linked to this new account.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 