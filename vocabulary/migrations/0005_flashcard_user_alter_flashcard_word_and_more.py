# Generated by Django 4.2.23 on 2025-07-08 05:50

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('vocabulary', '0004_flashcard_phonetic'),
    ]

    operations = [
        migrations.AddField(
            model_name='flashcard',
            name='user',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='flashcards', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='flashcard',
            name='word',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterUniqueTogether(
            name='flashcard',
            unique_together={('user', 'word')},
        ),
    ]
