# Generated by Django 5.2.1 on 2025-07-23 06:32

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('vocabulary', '0009_flashcard_correct_reviews_flashcard_difficulty_score_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='StudySession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_start', models.DateTimeField(auto_now_add=True)),
                ('session_end', models.DateTimeField(blank=True, null=True)),
                ('study_mode', models.CharField(choices=[('deck', 'Deck Study'), ('random', 'Random Study'), ('spaced_repetition', 'Spaced Repetition')], default='deck', max_length=20)),
                ('total_questions', models.PositiveIntegerField(default=0)),
                ('correct_answers', models.PositiveIntegerField(default=0)),
                ('incorrect_answers', models.PositiveIntegerField(default=0)),
                ('session_duration_seconds', models.PositiveIntegerField(default=0, help_text='Total session duration in seconds')),
                ('words_studied', models.PositiveIntegerField(default=0, help_text='Unique words encountered in this session')),
                ('average_response_time', models.FloatField(default=0.0, help_text='Average time per question in seconds')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('decks_studied', models.ManyToManyField(blank=True, help_text='Decks included in this session', to='vocabulary.deck')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='study_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-session_start'],
            },
        ),
        migrations.CreateModel(
            name='StudySessionAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_correct', models.BooleanField()),
                ('response_time_seconds', models.FloatField(help_text='Time taken to answer in seconds')),
                ('question_type', models.CharField(default='multiple_choice', help_text='Type of question (mc, input, etc.)', max_length=20)),
                ('difficulty_before', models.FloatField(help_text='Card difficulty before this answer')),
                ('difficulty_after', models.FloatField(help_text='Card difficulty after this answer')),
                ('answered_at', models.DateTimeField(auto_now_add=True)),
                ('flashcard', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='vocabulary.flashcard')),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='answers', to='vocabulary.studysession')),
            ],
            options={
                'ordering': ['answered_at'],
            },
        ),
        migrations.CreateModel(
            name='WeeklyStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.PositiveIntegerField()),
                ('week_number', models.PositiveIntegerField(help_text='ISO week number (1-53)')),
                ('week_start_date', models.DateField()),
                ('total_study_time_seconds', models.PositiveIntegerField(default=0)),
                ('total_questions_answered', models.PositiveIntegerField(default=0)),
                ('correct_answers', models.PositiveIntegerField(default=0)),
                ('incorrect_answers', models.PositiveIntegerField(default=0)),
                ('unique_words_studied', models.PositiveIntegerField(default=0)),
                ('study_sessions_count', models.PositiveIntegerField(default=0)),
                ('study_days_count', models.PositiveIntegerField(default=0, help_text='Number of days studied this week')),
                ('new_cards_created', models.PositiveIntegerField(default=0)),
                ('weekly_goal_met', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='weekly_stats', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-year', '-week_number'],
            },
        ),
        migrations.CreateModel(
            name='DailyStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('total_study_time_seconds', models.PositiveIntegerField(default=0)),
                ('total_questions_answered', models.PositiveIntegerField(default=0)),
                ('correct_answers', models.PositiveIntegerField(default=0)),
                ('incorrect_answers', models.PositiveIntegerField(default=0)),
                ('unique_words_studied', models.PositiveIntegerField(default=0)),
                ('study_sessions_count', models.PositiveIntegerField(default=0)),
                ('average_session_duration', models.FloatField(default=0.0, help_text='Average session duration in seconds')),
                ('new_cards_created', models.PositiveIntegerField(default=0)),
                ('is_study_day', models.BooleanField(default=False, help_text='True if user studied on this day')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='daily_stats', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-date'],
                'indexes': [models.Index(fields=['user', 'date'], name='vocabulary__user_id_ebb603_idx'), models.Index(fields=['user', 'is_study_day'], name='vocabulary__user_id_8a7f84_idx')],
                'unique_together': {('user', 'date')},
            },
        ),
        migrations.AddIndex(
            model_name='studysession',
            index=models.Index(fields=['user', 'session_start'], name='vocabulary__user_id_80e4a4_idx'),
        ),
        migrations.AddIndex(
            model_name='studysession',
            index=models.Index(fields=['user', 'study_mode'], name='vocabulary__user_id_1f998f_idx'),
        ),
        migrations.AddIndex(
            model_name='studysessionanswer',
            index=models.Index(fields=['session', 'answered_at'], name='vocabulary__session_70358a_idx'),
        ),
        migrations.AddIndex(
            model_name='studysessionanswer',
            index=models.Index(fields=['flashcard', 'is_correct'], name='vocabulary__flashca_cbdabf_idx'),
        ),
        migrations.AddIndex(
            model_name='weeklystatistics',
            index=models.Index(fields=['user', 'year', 'week_number'], name='vocabulary__user_id_19dc73_idx'),
        ),
        migrations.AddIndex(
            model_name='weeklystatistics',
            index=models.Index(fields=['user', 'week_start_date'], name='vocabulary__user_id_2c09dd_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='weeklystatistics',
            unique_together={('user', 'year', 'week_number')},
        ),
    ]
