{% extends "base.html" %}
{% load i18n %}

{% block title %}{{ texts.title }}{% endblock %}

{% block content %}
<div style="max-width: 800px; margin: 0 auto; padding: 20px;">
    <h1>🧪 {{ texts.title }}</h1>
    
    <div style="background: #232345; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <h3>🔍 Debug Information:</h3>
        <ul style="color: #b0b0b0; line-height: 1.8;">
            <li><strong>Current Language:</strong> {{ current_language }}</li>
            <li><strong>Session Language:</strong> {{ session_language }}</li>
            <li><strong>All Session Data:</strong> {{ all_session_data }}</li>
        </ul>
    </div>
    
    <div style="background: #2a2a40; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <h3>🌐 Manual Language Switch:</h3>
        <form action="/i18n/setlang/" method="post" style="display: flex; gap: 10px; align-items: center;">
            {% csrf_token %}
            <input name="next" type="hidden" value="/en{{ request.get_full_path|slice:'3:' }}" id="next-input" />
            
            <button type="submit" name="language" value="en" 
                    style="background: #4caf50; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                🇺🇸 English
            </button>
            
            <button type="submit" name="language" value="vi" 
                    style="background: #2196f3; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                🇻🇳 Tiếng Việt
            </button>
        </form>
    </div>
    
    <div style="background: #3a3a5c; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <h3>📝 Text Examples:</h3>
        <ul style="color: #ffffff; line-height: 2;">
            <li>{{ texts.welcome }}</li>
            <li>{{ texts.add_words }}</li>
            <li>{{ texts.flashcards }}</li>
            <li>{{ texts.navigation }}</li>
        </ul>
    </div>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="{% url 'dashboard' %}" style="background: #667eea; color: white; padding: 12px 24px; border-radius: 8px; text-decoration: none;">
            ← {{ texts.back }}
        </a>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const langButtons = document.querySelectorAll('button[name="language"]');
    const nextInput = document.getElementById('next-input');
    
    langButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetLang = this.value;
            const currentPath = window.location.pathname;
            
            // Remove current language prefix and add target language
            let newPath = currentPath;
            if (currentPath.startsWith('/vi/')) {
                newPath = currentPath.substring(3);
            } else if (currentPath.startsWith('/en/')) {
                newPath = currentPath.substring(3);
            }
            
            nextInput.value = '/' + targetLang + newPath;
            console.log('Language switch to:', targetLang, 'Next URL:', nextInput.value);
        });
    });
});
</script>
{% endblock %} 