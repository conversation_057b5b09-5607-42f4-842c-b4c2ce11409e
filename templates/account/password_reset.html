{% extends "account/base.html" %}
{% load i18n %}

{% block title %}{% trans "Password Reset" %}{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="max-w-md w-full space-y-8">
        <div class="bg-white rounded-lg shadow-xl p-8">
            <div class="text-center">
                <!-- Lock Icon -->
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-indigo-100 mb-4">
                    <svg class="h-8 w-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-9a2 2 0 00-2-2H9l-2 2H5a2 2 0 00-2 2v9a2 2 0 002 2z" />
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-gray-900 mb-2">
                    {% trans "Password Reset" %}
                </h2>
                
                <p class="text-gray-600 mb-6">
                    {% trans "Forgotten your password? Enter your email address below, and we'll send you an email allowing you to reset it." %}
                </p>
            </div>

            <div class="mt-8">
                <form method="post" action="{% url 'account_reset_password' %}" class="space-y-6">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="bg-red-50 border border-red-200 rounded-md p-4">
                            <div class="text-red-700 text-sm">
                                {{ form.non_field_errors }}
                            </div>
                        </div>
                    {% endif %}

                    <div>
                        <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            {% trans "Email Address" %}
                        </label>
                        <div class="mt-1">
                            {{ form.email }}
                        </div>
                        {% if form.email.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.email.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="flex flex-col space-y-4">
                        <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200">
                            {% trans "Reset My Password" %}
                        </button>

                        <div class="text-center">
                            <a href="{% url 'account_login' %}" class="text-indigo-600 hover:text-indigo-500 text-sm font-medium">
                                ← {% trans "Back to Login" %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Info Notice -->
            <div class="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700">
                            {% trans "Please check your email inbox and spam folder for the reset link." %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 