{% extends "account/base.html" %}
{% load i18n %}

{% block title %}{% trans "Set New Password" %}{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="max-w-md w-full space-y-8">
        <div class="bg-white rounded-lg shadow-xl p-8">
            <div class="text-center">
                <!-- Key Icon -->
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-indigo-100 mb-4">
                    <svg class="h-8 w-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-gray-900 mb-2">
                    {% trans "Set New Password" %}
                </h2>
                
                <p class="text-gray-600 mb-6">
                    {% trans "Please enter your new password below." %}
                </p>
            </div>

            {% if token_fail %}
                <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                {% trans "Invalid Reset Link" %}
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                <p>{% trans "The password reset link was invalid, possibly because it has already been used. Please request a new password reset." %}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex flex-col space-y-4">
                    <a href="{% url 'account_reset_password' %}" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200">
                        {% trans "Request New Reset Link" %}
                    </a>
                </div>
            {% else %}
                <div class="mt-8">
                    <form method="post" action="{{ action_url }}" class="space-y-6">
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                <div class="text-red-700 text-sm">
                                    {{ form.non_field_errors }}
                                </div>
                            </div>
                        {% endif %}

                        <div>
                            <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                {% trans "New Password" %}
                            </label>
                            <div class="mt-1">
                                {{ form.password1 }}
                            </div>
                            {% if form.password1.errors %}
                                <div class="text-red-600 text-sm mt-1">{{ form.password1.errors }}</div>
                            {% endif %}
                        </div>

                        <div>
                            <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                {% trans "Confirm New Password" %}
                            </label>
                            <div class="mt-1">
                                {{ form.password2 }}
                            </div>
                            {% if form.password2.errors %}
                                <div class="text-red-600 text-sm mt-1">{{ form.password2.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="flex flex-col space-y-4">
                            <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-200">
                                {% trans "Change Password" %}
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Password Requirements -->
                <div class="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">
                                {% trans "Password Requirements" %}
                            </h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <ul class="list-disc pl-5 space-y-1">
                                    <li>{% trans "At least 8 characters long" %}</li>
                                    <li>{% trans "Cannot be too similar to your email" %}</li>
                                    <li>{% trans "Cannot be a commonly used password" %}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="mt-6 text-center">
                <a href="{% url 'account_login' %}" class="text-indigo-600 hover:text-indigo-500 text-sm font-medium">
                    ← {% trans "Back to Login" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %} 