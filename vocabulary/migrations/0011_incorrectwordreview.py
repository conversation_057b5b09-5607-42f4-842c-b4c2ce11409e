# Generated by Django 5.2.1 on 2025-07-23 17:10

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('vocabulary', '0010_studysession_studysessionanswer_weeklystatistics_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='IncorrectWordReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_type', models.CharField(choices=[('mc', 'Multiple Choice'), ('type', 'Input Mode'), ('dictation', 'Dictation Mode')], max_length=10)),
                ('error_count', models.PositiveIntegerField(default=1, help_text='Number of times answered incorrectly in this question type')),
                ('first_error_date', models.DateTimeField(auto_now_add=True)),
                ('last_error_date', models.DateTimeField(auto_now=True)),
                ('is_resolved', models.BooleanField(default=False, help_text='True when answered correctly in this question type')),
                ('resolved_date', models.DateTimeField(blank=True, null=True)),
                ('flashcard', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='incorrect_reviews', to='vocabulary.flashcard')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='incorrect_words', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-last_error_date'],
                'indexes': [models.Index(fields=['user', 'is_resolved'], name='vocabulary__user_id_7ae0e2_idx'), models.Index(fields=['user', 'question_type', 'is_resolved'], name='vocabulary__user_id_9a8886_idx')],
                'unique_together': {('user', 'flashcard', 'question_type')},
            },
        ),
    ]
