/* ==========================================================================
   Enhanced Audio Selection Modal Styles
   ========================================================================== */

/* Modal Base Styles */
.audio-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.audio-modal.show {
  opacity: 1;
  visibility: visible;
}

.audio-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
}

.audio-modal-content {
  position: relative;
  background: #1f2937;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  max-width: 700px;
  width: 95%;
  max-height: 85vh;
  min-height: 400px;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
}

.audio-modal.show .audio-modal-content {
  transform: scale(1);
}

/* Modal Header */
.audio-modal-header {
  background: #374151;
  padding: 20px 24px;
  border-bottom: 1px solid #4b5563;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0; /* Prevent header from shrinking */
}

.audio-modal-title {
  color: #f9fafb;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
  margin-right: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.word-display {
  color: #6a6cff;
  font-weight: 700;
}

.audio-modal-close {
  background: none;
  border: none;
  color: #9ca3af;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease, background-color 0.2s ease;
}

.audio-modal-close:hover {
  color: #f9fafb;
  background-color: #4b5563;
}

/* Modal Body */
.audio-modal-body {
  padding: 24px;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0; /* Important for flex child to shrink */
}

/* Current Audio Section */
.current-audio-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #374151;
  border-radius: 8px;
  border: 1px solid #4b5563;
}

.current-audio-title {
  color: #f9fafb;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-audio-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: #4b5563;
  border-radius: 6px;
}

.current-audio-info {
  color: #d1d5db;
  font-size: 0.9rem;
}

.no-current-audio {
  color: #9ca3af;
  font-style: italic;
  font-size: 0.9rem;
}

/* Audio Options Container */
.audio-options-container {
  margin-bottom: 20px;
}

.audio-options-content {
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
}

.audio-options-title {
  color: #f9fafb;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Audio Option Styles */
.audio-option {
  background: #374151;
  border: 2px solid #4b5563;
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 16px;
  transition: border-color 0.2s ease, background-color 0.2s ease, transform 0.1s ease;
  cursor: pointer;
  user-select: none; /* Prevent text selection when clicking */
}

.audio-option:hover {
  border-color: #6b7280;
  background: #3f4651;
  transform: translateY(-1px);
}

.audio-option:active {
  transform: translateY(0);
}

.audio-option.selected {
  border-color: #6a6cff;
  background: #2d3748;
  box-shadow: 0 0 0 1px #6a6cff;
}

.audio-option.error {
  cursor: not-allowed;
  opacity: 0.6;
}

.audio-option.error:hover {
  transform: none;
  border-color: #4b5563;
  background: #374151;
}

.audio-option-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.audio-option input[type="radio"] {
  width: 18px;
  height: 18px;
  accent-color: #6a6cff;
}

.audio-label {
  color: #f9fafb;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  flex: 1;
}

.audio-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  flex-wrap: wrap;
  min-height: 40px;
}

.btn-preview {
  background: #6a6cff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background-color 0.2s ease, transform 0.1s ease;
  min-width: 80px;
  justify-content: center;
  flex-shrink: 0;
}

.btn-preview:hover {
  background: #5a5cff;
  transform: translateY(-1px);
}

.btn-preview:active {
  transform: translateY(0);
}

.btn-preview:disabled {
  background: #6b7280;
  cursor: not-allowed;
  transform: none;
}

.btn-preview.playing {
  background: #10b981;
}

.btn-preview.playing i {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.audio-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #6b7280;
}

.status-indicator.ready {
  background: #10b981;
}

.status-indicator.error {
  background: #ef4444;
}

.status-indicator.loading {
  background: #f59e0b;
  animation: pulse 1s infinite;
}

.status-text {
  color: #9ca3af;
  font-size: 0.8rem;
}

/* Loading State */
.audio-loading-state {
  text-align: center;
  padding: 40px 20px;
}

.audio-loading-spinner {
  font-size: 3rem;
  color: #6a6cff;
  margin-bottom: 16px;
}

.audio-loading-spinner i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.audio-loading-text {
  color: #f9fafb;
  font-size: 1.1rem;
  margin: 0;
}

/* No Options State */
.no-audio-options {
  text-align: center;
  padding: 40px 20px;
}

.no-audio-icon {
  font-size: 3rem;
  color: #6b7280;
  margin-bottom: 16px;
}

.no-audio-message {
  font-size: 1.1rem;
  color: #f9fafb;
  margin-bottom: 8px;
}

.no-audio-suggestion {
  color: #9ca3af;
  font-size: 0.9rem;
  margin: 0;
}

/* Error State */
.audio-error-state {
  text-align: center;
  padding: 40px 20px;
}

.audio-error-icon {
  font-size: 3rem;
  color: #ef4444;
  margin-bottom: 16px;
}

.audio-error-message {
  color: #f9fafb;
  font-size: 1.1rem;
  margin-bottom: 12px;
}

.audio-error-details {
  color: #9ca3af;
  font-size: 0.9rem;
  margin: 0;
}

/* Modal Footer */
.audio-modal-footer {
  background: #374151;
  padding: 20px 24px;
  border-top: 1px solid #4b5563;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  flex-shrink: 0; /* Prevent footer from shrinking */
  flex-wrap: wrap; /* Allow buttons to wrap on small screens */
}

.audio-modal-footer button {
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  min-width: 120px;
  white-space: nowrap;
}

.btn-cancel {
  background: #6b7280;
  color: white;
}

.btn-cancel:hover {
  background: #5b6470;
}

.btn-keep-current {
  background: #374151;
  color: #d1d5db;
  border: 1px solid #6b7280;
}

.btn-keep-current:hover {
  background: #4b5563;
  color: #f9fafb;
}

.btn-confirm-selection {
  background: #10b981;
  color: white;
}

.btn-confirm-selection:hover {
  background: #059669;
}

.btn-confirm-selection:disabled {
  background: #6b7280;
  cursor: not-allowed;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

/* Mobile Styles */
@media (max-width: 640px) {
  .audio-modal-content {
    width: 98%;
    max-width: none;
    max-height: 95vh;
    margin: 10px;
    min-height: 300px;
  }

  .audio-modal-header {
    padding: 16px 20px;
  }

  .audio-modal-title {
    font-size: 1.1rem;
    line-height: 1.3;
  }

  .audio-modal-body {
    padding: 16px 20px;
  }

  .audio-modal-footer {
    padding: 16px 20px;
    flex-direction: column;
    gap: 8px;
  }

  .audio-modal-footer button {
    width: 100%;
    min-width: auto;
    margin: 0;
    padding: 14px 20px;
  }

  .audio-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .btn-preview {
    justify-content: center;
    width: 100%;
    min-width: auto;
  }

  .audio-status {
    justify-content: center;
  }

  .current-audio-item {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .current-audio-info {
    text-align: center;
  }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
  .audio-modal-content {
    width: 100%;
    height: 100%;
    max-height: 100vh;
    border-radius: 0;
    margin: 0;
  }

  .audio-modal-header {
    padding: 12px 16px;
  }

  .audio-modal-title {
    font-size: 1rem;
    line-height: 1.2;
  }

  .audio-modal-body {
    padding: 12px 16px;
  }

  .audio-modal-footer {
    padding: 12px 16px;
  }

  .current-audio-item,
  .audio-option {
    padding: 12px;
  }

  .audio-loading-state,
  .no-audio-options,
  .audio-error-state {
    padding: 20px 12px;
  }

  .audio-loading-spinner,
  .no-audio-icon,
  .audio-error-icon {
    font-size: 2rem;
  }

  .audio-modal-footer button {
    padding: 16px 20px;
    font-size: 1rem;
  }
}

/* Tablet Styles */
@media (min-width: 641px) and (max-width: 1024px) {
  .audio-modal-content {
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
  }

  .audio-controls {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .audio-modal-footer {
    flex-direction: row;
    justify-content: flex-end;
  }

  .audio-modal-footer button {
    width: auto;
    min-width: 120px;
  }
}

/* Tablet landscape */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
  .audio-modal-content {
    max-width: 800px;
    max-height: 90vh;
  }

  .audio-controls {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  .audio-modal,
  .audio-modal-content,
  .btn-preview,
  .audio-modal-footer button {
    transition: none;
  }

  .audio-loading-spinner i,
  .status-indicator.loading,
  .btn-preview.playing i {
    animation: none;
  }
}

/* Focus States for Accessibility */
.audio-modal-close:focus,
.btn-preview:focus,
.audio-modal-footer button:focus,
.audio-option input[type="radio"]:focus {
  outline: 2px solid #6a6cff;
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .audio-modal-content {
    border: 2px solid #ffffff;
  }

  .audio-option {
    border: 2px solid #ffffff;
  }

  .audio-option.selected {
    border: 2px solid #6a6cff;
  }
}
