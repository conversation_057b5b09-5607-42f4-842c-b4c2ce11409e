{% extends "base.html" %}
{% load static %}

{% block title %}
{{ manual_texts.favorites }} - {{ manual_texts.learn_english }}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/deck_detail.css' %}" />
<style>
.favorites-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.favorites-header {
  text-align: center;
  margin-bottom: 40px;
}

.favorites-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.favorites-subtitle {
  font-size: 1.1rem;
  color: var(--text-muted);
  margin-bottom: 20px;
}

.favorites-stats {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 30px;
}

.stat-item {
  text-align: center;
  padding: 15px 25px;
  background: var(--card-background);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  display: block;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-muted);
  margin-top: 5px;
}

.favorites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.favorite-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
}

.favorite-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.favorite-word {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.favorite-phonetic {
  font-size: 1rem;
  color: var(--text-muted);
  font-style: italic;
  margin-bottom: 10px;
}

.favorite-definitions {
  margin-bottom: 15px;
}

.definition-item {
  margin-bottom: 8px;
  padding: 8px 12px;
  background: var(--background-secondary);
  border-radius: 6px;
  border-left: 3px solid var(--primary-color);
}

.definition-english {
  font-weight: 600;
  color: var(--text-main);
  margin-bottom: 4px;
}

.definition-vietnamese {
  color: var(--text-muted);
  font-size: 0.9rem;
}

.favorite-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
}

.unfavorite-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.unfavorite-btn:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

.favorite-date {
  font-size: 0.8rem;
  color: var(--text-muted);
}

.no-favorites {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-muted);
}

.no-favorites h2 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: var(--text-main);
}

.no-favorites p {
  font-size: 1.1rem;
  margin-bottom: 25px;
}

.no-favorites a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
}

.no-favorites a:hover {
  text-decoration: underline;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.pagination {
  display: flex;
  gap: 10px;
  align-items: center;
}

.pagination a,
.pagination span {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  text-decoration: none;
  color: var(--text-main);
  transition: all 0.3s ease;
}

.pagination a:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination .current {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

@media (max-width: 768px) {
  .favorites-grid {
    grid-template-columns: 1fr;
  }
  
  .favorites-stats {
    flex-direction: column;
    gap: 15px;
  }
  
  .stat-item {
    padding: 12px 20px;
  }
}
</style>
{% endblock %}

{% block content %}
<div class="favorites-page">
  <div class="favorites-header">
    <h1 class="favorites-title">❤️ {{ manual_texts.my_favorites|default:"My Favorites" }}</h1>
    <p class="favorites-subtitle">{{ manual_texts.favorites_description|default:"Your favorite vocabulary words for focused study" }}</p>
    
    <div class="favorites-stats">
      <div class="stat-item">
        <span class="stat-number">{{ total_favorites }}</span>
        <div class="stat-label">{{ manual_texts.favorite_words|default:"Favorite Words" }}</div>
      </div>
      <div class="stat-item">
        <span class="stat-number">{{ favorites.paginator.num_pages }}</span>
        <div class="stat-label">{{ manual_texts.pages|default:"Pages" }}</div>
      </div>
    </div>
  </div>

  {% if favorites %}
    <div class="favorites-grid">
      {% for favorite in favorites %}
        <div class="favorite-card" data-card-id="{{ favorite.flashcard.id }}">
          <div class="favorite-word">
            <a href="https://dictionary.cambridge.org/dictionary/english/{{ favorite.flashcard.word }}" 
               target="_blank" 
               class="dictionary-word-link"
               data-word="{{ favorite.flashcard.word }}">
              {{ favorite.flashcard.word }}
            </a>
            {% if favorite.flashcard.audio_url %}
              <button class="audio-btn" data-audio-url="{{ favorite.flashcard.audio_url }}" title="{{ manual_texts.play_audio|default:'Play Audio' }}">
                🔊
              </button>
            {% endif %}
          </div>
          
          {% if favorite.flashcard.phonetic %}
            <div class="favorite-phonetic">/{{ favorite.flashcard.phonetic }}/</div>
          {% endif %}
          
          <div class="favorite-definitions">
            {% for definition in favorite.flashcard.definitions.all %}
              <div class="definition-item">
                <div class="definition-english">{{ definition.english_definition }}</div>
                <div class="definition-vietnamese">{{ definition.vietnamese_definition }}</div>
              </div>
            {% endfor %}
          </div>
          
          <div class="favorite-actions">
            <button class="unfavorite-btn" data-card-id="{{ favorite.flashcard.id }}">
              💔 {{ manual_texts.remove_favorite|default:"Remove" }}
            </button>
            <div class="favorite-date">
              {{ manual_texts.added|default:"Added" }}: {{ favorite.favorited_at|date:"M d, Y" }}
            </div>
          </div>
        </div>
      {% endfor %}
    </div>

    <!-- Pagination -->
    {% if favorites.has_other_pages %}
      <div class="pagination-container">
        <div class="pagination">
          {% if favorites.has_previous %}
            <a href="?page=1">&laquo; {{ manual_texts.first|default:"First" }}</a>
            <a href="?page={{ favorites.previous_page_number }}">{{ manual_texts.previous|default:"Previous" }}</a>
          {% endif %}
          
          <span class="current">
            {{ manual_texts.page|default:"Page" }} {{ favorites.number }} {{ manual_texts.of|default:"of" }} {{ favorites.paginator.num_pages }}
          </span>
          
          {% if favorites.has_next %}
            <a href="?page={{ favorites.next_page_number }}">{{ manual_texts.next|default:"Next" }}</a>
            <a href="?page={{ favorites.paginator.num_pages }}">{{ manual_texts.last|default:"Last" }} &raquo;</a>
          {% endif %}
        </div>
      </div>
    {% endif %}
  {% else %}
    <div class="no-favorites">
      <h2>{{ manual_texts.no_favorites_yet|default:"No favorites yet" }}</h2>
      <p>{{ manual_texts.start_favoriting|default:"Start adding words to your favorites from deck pages or during study sessions!" }}</p>
      <p>
        <a href="{% url 'deck_list' %}">{{ manual_texts.browse_decks|default:"Browse your decks" }}</a> {{ manual_texts.or|default:"or" }}
        <a href="{% url 'study' %}">{{ manual_texts.start_studying|default:"start studying" }}</a>
      </p>
    </div>
  {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle unfavorite buttons
    const unfavoriteButtons = document.querySelectorAll('.unfavorite-btn');
    
    unfavoriteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const cardId = this.getAttribute('data-card-id');
            const card = this.closest('.favorite-card');
            
            // Show loading state
            this.disabled = true;
            this.innerHTML = '⏳ {{ manual_texts.removing|default:"Removing..." }}';
            
            // Call API to toggle favorite (remove)
            fetch('/api/favorites/toggle/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    card_id: cardId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove card with animation
                    card.style.transition = 'all 0.3s ease';
                    card.style.opacity = '0';
                    card.style.transform = 'scale(0.8)';
                    
                    setTimeout(() => {
                        card.remove();
                        
                        // Check if no more favorites
                        const remainingCards = document.querySelectorAll('.favorite-card');
                        if (remainingCards.length === 0) {
                            location.reload(); // Reload to show "no favorites" message
                        }
                    }, 300);
                } else {
                    alert('{{ manual_texts.error_removing_favorite|default:"Error removing favorite" }}: ' + data.error);
                    // Reset button
                    this.disabled = false;
                    this.innerHTML = '💔 {{ manual_texts.remove_favorite|default:"Remove" }}';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('{{ manual_texts.error_removing_favorite|default:"Error removing favorite" }}');
                // Reset button
                this.disabled = false;
                this.innerHTML = '💔 {{ manual_texts.remove_favorite|default:"Remove" }}';
            });
        });
    });
    
    // Handle audio buttons
    const audioButtons = document.querySelectorAll('.audio-btn');
    
    audioButtons.forEach(button => {
        button.addEventListener('click', function() {
            const audioUrl = this.getAttribute('data-audio-url');
            if (audioUrl) {
                const audio = new Audio(audioUrl);
                audio.play().catch(error => {
                    console.error('Error playing audio:', error);
                });
            }
        });
    });
});
</script>
{% endblock %}
