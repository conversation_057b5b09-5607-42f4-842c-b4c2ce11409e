{% extends "account/base.html" %}
{% load socialaccount %}

{% block title %}{{ manual_texts.login_title }}{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="max-w-md w-full space-y-8">
        <div class="bg-white rounded-lg shadow-xl p-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">{{ manual_texts.welcome_back }}</h2>
                <p class="text-gray-600">{{ manual_texts.login_subtitle }}</p>
            </div>

            <div class="mt-8 space-y-6">
                <!-- Google Login Button -->
                <div class="text-center">
                    <a href="{% provider_login_url 'google' %}"
                       class="btn-google w-full flex justify-center items-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-200">
                        <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        {{ manual_texts.login_with_google }}
                    </a>
                </div>

                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">{{ manual_texts.or }}</span>
                    </div>
                </div>

                <!-- Email Login Form -->
                <form method="post" action="{% url 'account_login' %}" class="space-y-6">
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="bg-red-50 border border-red-200 rounded-md p-4">
                            <div class="text-red-700 text-sm">
                                {{ form.non_field_errors }}
                            </div>
                        </div>
                    {% endif %}

                    <div>
                        <label for="{{ form.login.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            {{ manual_texts.email_address }}
                        </label>
                        <div class="mt-1">
                            {{ form.login }}
                        </div>
                        {% if form.login.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.login.errors }}</div>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.password.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            {{ manual_texts.password }}
                        </label>
                        <div class="mt-1">
                            {{ form.password }}
                        </div>
                        {% if form.password.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.password.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            {{ form.remember }}
                            <label for="{{ form.remember.id_for_label }}" class="ml-2 block text-sm text-gray-900">
                                {{ manual_texts.remember_me }}
                            </label>
                        </div>

                        <div class="text-sm">
                            <a href="{% url 'account_reset_password' %}" class="font-medium text-indigo-600 hover:text-indigo-500">
                                {{ manual_texts.forgot_password }}
                            </a>
                        </div>
                    </div>

                    <div>
                        <button type="submit" class="btn-primary group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200">
                            {{ manual_texts.sign_in }}
                        </button>
                    </div>

                    <div class="text-center">
                        <span class="text-gray-600">{{ manual_texts.dont_have_account }}</span>
                        <a href="{% url 'account_signup' %}" class="font-medium text-indigo-600 hover:text-indigo-500 ml-1">
                            {{ manual_texts.sign_up }}
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


{% endblock %} 