{% extends "account/base.html" %}
{% load i18n %}

{% block title %}{% trans "Verification Email Sent" %}{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="max-w-md w-full space-y-8">
        <div class="bg-white rounded-lg shadow-xl p-8">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
                    <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                </div>
                <h2 class="text-2xl font-bold text-gray-900 mb-2">{% trans "Check Your Email" %}</h2>
                <p class="text-gray-600 mb-6">{% trans "We've sent a verification link to your email address. Please check your inbox and click the link to activate your account." %}</p>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700">
                            <strong>{% trans "Didn't receive the email?" %}</strong><br>
                            {% trans "Check your spam folder or" %} 
                            <a href="{% url 'account_email' %}" class="underline hover:text-blue-800">{% trans "resend verification email" %}</a>
                        </p>
                    </div>
                </div>
            </div>

            <div class="text-center">
                <a href="{% url 'account_login' %}" class="text-indigo-600 hover:text-indigo-500 text-sm">
                    {% trans "Back to Login" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %} 