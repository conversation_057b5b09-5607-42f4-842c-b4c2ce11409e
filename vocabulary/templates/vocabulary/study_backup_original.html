{% extends "base.html" %} {% load static %} {% block title %} {{
manual_texts.study }} - {{ manual_texts.learn_english }} {% endblock %} {% block
extra_css %}
<link rel="stylesheet" href="{% static 'css/study.css' %}" />
{% endblock %} {% block content %}
<div class="study-page">
  <div class="study-header">
    <h1 class="study-title">📖 {{ manual_texts.study }}</h1>
    <p class="study-subtitle">{{ manual_texts.platform_description }}</p>
  </div>

  <!-- Study Mode Selection -->
  <div class="study-mode-section">
    <h2 class="section-title">{{ manual_texts.study_mode }}</h2>

    <!-- Mode Slider Container -->
    <div class="mode-slider-container">
      <!-- Navigation Arrows -->
      <button
        class="slider-nav slider-nav-prev"
        id="sliderPrev"
        aria-label="Previous mode"
      >
        <span class="nav-arrow">‹</span>
      </button>

      <!-- Slider Wrapper -->
      <div
        class="mode-slider-wrapper"
        role="region"
        aria-label="Study mode selection"
      >
        <div class="mode-slider" id="modeSlider" role="tablist">
          <!-- Deck Study Mode -->
          <div
            class="mode-slide active"
            data-mode="decks"
            role="tabpanel"
            aria-label="Deck study mode"
          >
            <label class="mode-option">
              <input
                type="radio"
                name="study_mode"
                value="decks"
                checked
                aria-describedby="deck-description"
              />
              <div class="mode-card">
                <div class="mode-icon" aria-hidden="true">📚</div>
                <div class="mode-content">
                  <h3>{{ manual_texts.normal_study_by_decks }}</h3>
                  <p id="deck-description">
                    {{ manual_texts.deck_study_description }}
                  </p>
                </div>
              </div>
            </label>
          </div>

          <!-- Random Study Mode -->
          <div
            class="mode-slide"
            data-mode="random"
            role="tabpanel"
            aria-label="Random study mode"
          >
            <label class="mode-option">
              <input
                type="radio"
                name="study_mode"
                value="random"
                aria-describedby="random-description"
              />
              <div class="mode-card">
                <div class="mode-icon" aria-hidden="true">🎲</div>
                <div class="mode-content">
                  <h3>{{ manual_texts.study_random_words }}</h3>
                  <p id="random-description">
                    {{ manual_texts.random_study_description }}
                  </p>
                </div>
              </div>
            </label>
          </div>

          <!-- Review Mode -->
          <div
            class="mode-slide"
            data-mode="review"
            role="tabpanel"
            aria-label="Review incorrect words mode"
          >
            <label class="mode-option" id="reviewModeOption">
              <input
                type="radio"
                name="study_mode"
                value="review"
                aria-describedby="review-description"
              />
              <div class="mode-card">
                <div class="mode-icon" aria-hidden="true">🔄</div>
                <div class="mode-content">
                  <h3>{{ manual_texts.review_incorrect_words }}</h3>
                  <p id="review-description">
                    {{ manual_texts.review_study_description }}
                  </p>
                  <div
                    class="review-count"
                    id="reviewCount"
                    style="display: none"
                  >
                    <span id="reviewCountText"
                      >0 {{ manual_texts.incorrect_words_count }}</span
                    >
                  </div>
                </div>
              </div>
            </label>
          </div>
        </div>
      </div>

      <!-- Navigation Arrows -->
      <button
        class="slider-nav slider-nav-next"
        id="sliderNext"
        aria-label="Next mode"
      >
        <span class="nav-arrow">›</span>
      </button>
    </div>

    <!-- Slider Indicators -->
    <div class="slider-indicators">
      <button
        class="indicator active"
        data-slide="0"
        aria-label="Deck study mode"
      ></button>
      <button
        class="indicator"
        data-slide="1"
        aria-label="Random study mode"
      ></button>
      <button
        class="indicator"
        data-slide="2"
        aria-label="Review mode"
      ></button>
    </div>
  </div>

  <!-- Deck Study Options -->
  <div id="deckStudyOptions" class="study-options-section">
    <h3 class="options-title">{{ manual_texts.select_decks }}</h3>

    <div class="deck-selector">
      <button id="deckDropdownToggle" class="dropdown-toggle">
        <span id="selectedDecksText">{{ manual_texts.no_decks_selected }}</span>
        <i class="fas fa-chevron-down dropdown-arrow"></i>
      </button>

      <div id="deckDropdown" class="dropdown-menu">
        {% for d in decks %}
        <label class="deck-option">
          <input type="checkbox" name="deck_ids" value="{{ d.id }}" />
          <span class="deck-name">{{ d.name }}</span>
          <span class="deck-count"
            >{{ d.flashcards.count }} {{ manual_texts.words_unit }}</span
          >
        </label>
        {% empty %}
        <p class="no-decks-message">{{ manual_texts.no_decks_available }}</p>
        {% endfor %}
      </div>
    </div>

    <button id="startBtn" class="start-button">
      <span class="button-icon">🚀</span>
      {{ manual_texts.start_study }}
    </button>
  </div>

  <!-- Random Study Options -->
  <div id="randomStudyOptions" class="study-options-section hidden">
    <h3 class="options-title">{{ manual_texts.study_random_words }}</h3>

    <div class="random-options">
      <div class="word-count-input">
        <label for="randomWordCount">{{ manual_texts.number_of_words }}</label>
        <input
          type="number"
          id="randomWordCount"
          min="1"
          max="100"
          value="10"
        />
      </div>

      <div class="available-words-info">
        <span class="info-label">{{ manual_texts.available_words }}:</span>
        <span id="totalWordsAvailable" class="info-value"
          >{{ total_words_available }}</span
        >
      </div>
    </div>

    <button id="startRandomBtn" class="start-button">
      <span class="button-icon">🎲</span>
      {{ manual_texts.start_study }}
    </button>
  </div>

  <!-- Review Study Options -->
  <div id="reviewStudyOptions" class="study-options-section hidden">
    <h3 class="options-title">{{ manual_texts.review_incorrect_words }}</h3>

    <div class="review-options">
      <div class="review-info">
        <div class="review-description">
          <p>{{ manual_texts.review_mode_description }}</p>
          <div class="review-details" id="reviewDetails" style="display: none">
            <div class="review-breakdown">
              <div class="breakdown-item">
                <span class="breakdown-label"
                  >{{ manual_texts.multiple_choice }}:</span
                >
                <span class="breakdown-count" id="mcCount">0</span>
              </div>
              <div class="breakdown-item">
                <span class="breakdown-label"
                  >{{ manual_texts.input_mode }}:</span
                >
                <span class="breakdown-count" id="typeCount">0</span>
              </div>
              <div class="breakdown-item">
                <span class="breakdown-label"
                  >{{ manual_texts.dictation_mode }}:</span
                >
                <span class="breakdown-count" id="dictationCount">0</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <button id="startReviewBtn" class="start-button" disabled>
      <span class="button-icon">🔄</span>
      {{ manual_texts.start_review }}
    </button>
  </div>

  <div id="studyArea" class="study-area">
    <div class="study-header-bar">
      <button id="backBtn" class="back-button">
        <i class="fas fa-arrow-left"></i>
        {{ manual_texts.back_to_selection }}
      </button>
      <div class="header-controls">
        <div id="statsInfo" class="stats-display">
          {{ manual_texts.correct }}: 0 | {{ manual_texts.incorrect }}: 0
        </div>
        <div class="audio-settings">
          <label class="audio-toggle">
            <input type="checkbox" id="audioToggle" checked />
            <span class="toggle-slider"></span>
            <span class="toggle-label">
              <i class="fas fa-volume-up"></i>
              {{ manual_texts.sound_feedback }}
            </span>
          </label>
        </div>
      </div>
    </div>

    <div id="cardBox" class="flashcard-container">
      <div class="card-header">
        <div class="word-section">
          <h2 id="cardWord" class="card-word"></h2>
          <button id="audioButton" class="audio-button" style="display: none">
            <i class="fas fa-volume-up"></i>
          </button>
        </div>
        <p id="cardPhonetic" class="card-phonetic"></p>
      </div>

      <img id="cardImage" src="" alt="image" class="card-image" />

      <div id="answerSection" class="answer-section">
        <div id="cardDefs" class="card-definitions"></div>
        <div id="optionsArea" class="options-area"></div>
        <div id="feedbackMsg" class="feedback-message"></div>
      </div>

      <div id="gradeButtons" class="grade-buttons">
        <button class="grade-btn grade-again" data-grade="0">
          <span class="grade-icon">😰</span>
          {{ manual_texts.grade_again }}
        </button>
        <button class="grade-btn grade-hard" data-grade="1">
          <span class="grade-icon">😅</span>
          {{ manual_texts.grade_hard }}
        </button>
        <button class="grade-btn grade-good" data-grade="2">
          <span class="grade-icon">😊</span>
          {{ manual_texts.grade_good }}
        </button>
        <button class="grade-btn grade-easy" data-grade="3">
          <span class="grade-icon">😎</span>
          {{ manual_texts.grade_easy }}
        </button>
      </div>

      <p id="noCardMsg" class="no-cards-message">
        {{ manual_texts.no_cards_due }}
      </p>
    </div>
  </div>
</div>

<!-- Review Completion Success Modal -->
<div id="reviewCompletionModal" class="modal-overlay" style="display: none">
  <div class="modal-content success-modal">
    <div class="modal-header">
      <h2 class="modal-title">{{ manual_texts.review_completed_title }}</h2>
    </div>
    <div class="modal-body">
      <div class="success-icon">🎉</div>
      <p class="success-message">{{ manual_texts.review_completed_message }}</p>
    </div>
    <div class="modal-footer">
      <button id="continueStudyingBtn" class="modal-button primary">
        {{ manual_texts.continue_studying }}
      </button>
    </div>
  </div>
</div>

{% endblock %} {% block extra_js %}
<script src="{% static 'js/dictionary-utils.js' %}"></script>
<script>
  const STUDY_CFG = {
    nextUrl: "{% url 'api_next_question' %}",
    submitUrl: "{% url 'api_submit_answer' %}",
    csrfToken: document.querySelector('meta[name="csrf-token"]').content,
    labels: {
      correct: "{{ manual_texts.correct }}",
      incorrect: "{{ manual_texts.incorrect }}",
      check: "{{ manual_texts.check }}",
      placeholder: "{{ manual_texts.answer_placeholder }}",
      grade_again: "{{ manual_texts.grade_again }}",
      grade_hard: "{{ manual_texts.grade_hard }}",
      grade_good: "{{ manual_texts.grade_good }}",
      grade_easy: "{{ manual_texts.grade_easy }}",

      no_cards_due: "{{ manual_texts.no_cards_due }}",
      play_audio: "{{ manual_texts.play_audio }}",
      listen_and_type: "{{ manual_texts.listen_and_type }}",
      type_what_you_hear: "{{ manual_texts.type_what_you_hear }}",
      correct_answer: "{{ manual_texts.correct_answer }}",
      incorrect_answer: "{{ manual_texts.incorrect_answer }}",
      replay_audio: "{{ manual_texts.replay_audio }}",
      english_label: "{{ manual_texts.english_label }}",
      vietnamese_label: "{{ manual_texts.vietnamese_label }}",
      review_completed_title: "{{ manual_texts.review_completed_title }}",
      review_completed_message: "{{ manual_texts.review_completed_message }}",
      continue_studying: "{{ manual_texts.continue_studying }}",
    },
  };
</script>
<script src="{% static 'js/dictionary-utils.js' %}"></script>
<script src="{% static 'js/study.js' %}"></script>
{% endblock %}
