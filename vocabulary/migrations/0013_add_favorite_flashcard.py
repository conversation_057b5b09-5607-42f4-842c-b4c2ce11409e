# Generated by Django 5.1.7 on 2025-07-27 13:00

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('vocabulary', '0012_switch_to_difficulty_based_system'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FavoriteFlashcard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('favorited_at', models.DateTimeField(auto_now_add=True)),
                ('flashcard', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorited_by', to='vocabulary.flashcard')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorite_flashcards', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-favorited_at'],
                'indexes': [models.Index(fields=['user', 'favorited_at'], name='vocabulary__user_id_62c310_idx'), models.Index(fields=['user', 'flashcard'], name='vocabulary__user_id_9232ff_idx')],
                'unique_together': {('user', 'flashcard')},
            },
        ),
    ]
